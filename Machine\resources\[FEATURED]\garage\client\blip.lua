local blips = {
    {title="Garasi Import", colour=26, id=530, x = -501.1828, y = -2170.6438, z = 8.3163}, ---- import
    {title="NIX GALAXY CLUB", colour=1, id=121, x = 324.5651, y = 277.1135, z = 115.0111}, ---- import 324.5651, 277.1135, 115.0111, 91.8023
    {title="BYTE & BITE", colour=3, id=899, x = -20.3550, y = -97.2104, z = 57.1074}, -- -20.3550, -97.2104, 57.1074, 349.1154
    {title="KLINIK MANCHINEEL", colour=1, id=61, x = -509.2921, y = 2743.0137, z = 48.2033}, -- -509.2921, 2743.0137, 48.2033, 144.1365 -541.2327, -180.2730, 38.2291
    {title="Department Of Justice", colour=1, id=594, x = -541.2327, y = -180.2730, z = 38.2291}, --  -541.2327, -180.2730, 38.2291


}

local helipad = {
    {title="Garasi Helipad Pelabuhan", colour=26, id=64, x = -726.1718, y = -1446.0117, z = 5.3881},
    {title="Garasi Helipad SS", colour=26, id=64, x = 1728.6595, y = 3864.0264, z = 41.4838},
    {title="Garasi Heli Ambulance", colour=26, id=64, x = -2798.3542, y = -64.0585, z = 58.2805},
    {title="Garasi Heli Kertagarage", colour=26, id=64, x = 614.5342, y = -213.4478, z = 55.1541},
    {title="Impound Boat", colour=26, id=410, x = -796.5285, y = -1493.3669, z =  1.5952},
    {title="Garasi Heli Sandy", colour=26, id=64, x = 1775.0938, y = 3332.7222, z = 42.0909},
    {title="Garasi Boat Pelabuhan", colour=26, id=427, x = -853.6220, y = -1369.6600, z = -0.9417},
    {title="Garasi Boat Karnaval", colour=26, id=427, x = -1793.4000, y = -1230.2106, z = -0.9186},
}

Citizen.CreateThread(function()
  -- BLIP UMUM
  for _, info in pairs(blips) do
      info.blip = AddBlipForCoord(info.x, info.y, info.z)
      SetBlipSprite(info.blip, info.id)
      SetBlipDisplay(info.blip, 4)
      SetBlipScale(info.blip, 1.0)
      SetBlipColour(info.blip, info.colour)
      SetBlipAsShortRange(info.blip, true)
      BeginTextCommandSetBlipName("STRING")
      AddTextComponentString(info.title)
      EndTextCommandSetBlipName(info.blip)
  end

  -- BLIP HELIPAD / BOAT
  for _, info in pairs(helipad) do
      info.blip = AddBlipForCoord(info.x, info.y, info.z)
      SetBlipSprite(info.blip, info.id)
      SetBlipDisplay(info.blip, 4)
      SetBlipScale(info.blip, 0.6)
      SetBlipColour(info.blip, info.colour)
      SetBlipAsShortRange(info.blip, true)
      SetBlipCategory(info.blip, 135)
      BeginTextCommandSetBlipName("STRING")
      AddTextComponentString(info.title)
      EndTextCommandSetBlipName(info.blip)
  end

  -- BLIP GARASI KENDARAAN (DARI CONFIG)
  for k, v in pairs(Config.CarGarages) do
      local coords = v.GaragePoint
      local blip = AddBlipForCoord(coords.x, coords.y, coords.z or 0.0)
      SetBlipSprite(blip, Config.BlipGarage.Sprite)
      SetBlipDisplay(blip, 4)
      SetBlipScale(blip, 0.6)
      SetBlipColour(blip, Config.BlipGarage.Color)
      SetBlipAsShortRange(blip, true)
      SetBlipCategory(blip, 134)
      BeginTextCommandSetBlipName("STRING")
      AddTextComponentString(v.Name)
      EndTextCommandSetBlipName(blip)
  end
end)
