Locales['en'] = {
	-- Global
	['custom_kick'] = 'INPUT CUSTOM KICK MESSAGE',
	['blip_garage'] = 'Garage | Public',
	['blip_garage_private'] = 'Garage | Private',
	['blip_pound'] = 'Garage | Pound',
	['loc_garage'] = 'In Garage',
	['loc_pound'] = 'Check Impound',
	['return'] = 'Return',
	['store_vehicles'] = 'Store Vehicle in Garage.',
	['press_to_enter'] = 'Press ~INPUT_PICKUP~ to take Vehicle out of Garage.',
	['press_to_delete'] = 'Press ~INPUT_PICKUP~ to store Vehicle in the Garage.',
	['press_to_impound'] = 'Press ~INPUT_PICKUP~ to access the Impound.',
	['you_paid'] = 'You paid $',
	['not_enough_money'] = 'You do not have enough money!',
	['return_vehicle'] = 'Store Vehicle.',
	['see_mechanic'] = 'Visit Mechanic.',
	['damaged_vehicle'] = 'Vehicle Damaged!',
	['visit_mechanic'] = 'Visit the Mechanic or Repair yourself.',
	['cannot_store_vehicle'] = 'You can not store this Vehicle!',
	['no_vehicle_to_enter'] = 'There is no Vehicle to store in the Garage.',
	['vehicle_in_garage'] = 'Your Vehicle is stored in the Garage.',
	['must_wait'] = 'You must Wait ~r~ %s Minutes ~w~before Accessing Impound Again!',
	['get_properties'] = '~y~Getting Private Garages!',
	['driver_seat'] = 'Must be in Driver Seat to Store Vehicle!',
	['not_correct_veh'] = 'Not in Correct Vehicle for this Garage!',
	['in_veh'] = 'Must be out of a Vehicle to Access this!',
	['cant_take_out'] = 'Can\'t take Vehicle Out.',
	-- Spacers
	['spacer1'] = 'If Vehicle is NOT here Check Impound!!!',
	['spacer2'] = '---- Aircrafts ----',
	['plate'] = 'Plate',
	['vehicle'] = 'Vehicle Name',
	['location'] = 'Location',
	-- Ambulance
	['blip_ambulance_garage'] = 'Garage | Ambulance Garage',
	['blip_ambulance_pound'] = 'Garage | Ambulance Pound',
	['garage_ambulance'] = 'Ambulance Garage',
	['pound_ambulance'] = 'Ambulance Pound: Fee $%s',
	['garage_no_ambulance'] = 'You dont own any Ambulance Vehicles!',
	['garage_no_ambulance_aircraft'] = 'You dont own any Ambulance Aircrafts!',
	['ambulance_is_impounded'] = 'Your Ambulance Vehicle is not here.',
	['must_ambulance'] = 'Must be Ambulance Job to Access this!',
	-- Police
	['blip_police_garage'] = 'Garage | Police Garage',
	['blip_police_pound'] = 'Garage | Police Pound',
	['garage_police'] = 'Police Garage',
	['pound_police'] = 'Police Pound: Fee $%s',
	['garage_no_police'] = 'You dont own any Police Vehicles!',
	['garage_no_police_aircraft'] = 'You dont own any Police Aircrafts!',
	['police_is_impounded'] = 'Your Police Vehicle is not here.',
	['must_police'] = 'Must be Police Job to Access this!',
	-- Mechanic
	['blip_mechanic_garage'] = 'Garage | Mechanic Garage',
	['blip_mechanic_pound'] = 'Garage | Mechanic Pound',
	['garage_mechanic'] = 'Mechanic Garage',
	['pound_mechanic'] = 'Mechanic Pound: Fee $%s',
	['garage_no_mechanic'] = 'You dont own any Mechanic Vehicles!',
	['garage_no_mechanic_aircraft'] = 'You dont own any Mechanic Aircrafts!',
	['mechanic_is_impounded'] = 'Your Mechanic Vehicle is not here.',
	['must_mechanic'] = 'Must be Mechanic Job to Access this!',
	-- Aircrafts
	['garage_aircrafts'] = 'Aircraft Garage',
	['pound_aircrafts'] = 'Aircraft Pound: Fee $%s',
	['garage_no_aircrafts'] = 'You dont own any Aircrafts!',
	['aircraft_is_impounded'] = 'Your Aircraft is not here.',
	-- Boats
	['garage_boats'] = 'Boat Garage',
	['pound_boats'] = 'Boat Pound: Fee $%s',
	['garage_no_boats'] = 'You dont own any Boats!',
	['boat_is_impounded'] = 'Your Boat is not here.',
	-- Cars
	['garage_cars'] = 'Car Garage',
	['pound_cars'] = 'Car Pound: Fee $%s',
	['garage_no_cars'] = 'You dont own any Cars!',
	['car_is_impounded'] = 'Your Car is not here.',
}