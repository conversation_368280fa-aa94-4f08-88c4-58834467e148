local ESX = exports["r_core"]:getSharedObject()


ESX.RegisterServerCallback('', function(source, cb, plate)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE plate = @plate', {
        ['@plate'] = plate,
    }, function(result)
        for _, v in pairs(result) do
            local vehicle = json.decode(v.vehicle)
            table.insert(result, { trunk = v.trunk }) -- Perhatian: Ini memodifikasi tabel 'result' saat iterasi, bisa berpotensi masalah. Sebaiknya buat tabel baru.
        end
        cb(result)
    end)
end)

ESX.RegisterServerCallback('garage:getTrunkInventory', function(source, cb, plate)
    if plate == nil then
        return
    end
    MySQL.Async.fetchAll('SELECT trunk FROM owned_vehicles WHERE plate = @plate LIMIT 1', {
        ['@plate'] = plate
    }, function(result)
        cb(result)
    end)
end)

-- Add Command for Getting Properties
if Config.Main.Commands then
    ESX.RegisterCommand('getgarages', 'user', function(xPlayer, args, showError)
        xPlayer.triggerEvent('tirc-garasi:getPropertiesC')
    end, true, { help = 'Get Private Garages', validate = false })
end

-- Add Print Command for Getting Properties
RegisterServerEvent('tirc-garasi:printGetProperties')
AddEventHandler('tirc-garasi:printGetProperties', function()
    -- print('Getting Properties')
end)

-- Get Owned Properties
ESX.RegisterServerCallback('tirc-garasi:getOwnedProperties', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    local properties = {}

    MySQL.Async.fetchAll('SELECT * FROM owned_properties WHERE owner = @owner', {
        ['@owner'] = xPlayer.identifier
    }, function(data)
        for _, v in pairs(data) do
            table.insert(properties, v.name)
        end
        cb(properties)
    end)
end)

-- Start of Ambulance Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedAmbulanceCars', function(source, cb)
    local ownedAmbulanceCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'ambulance'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedAmbulanceCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedAmbulanceCars)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'ambulance',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedAmbulanceCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedAmbulanceCars)
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOwnedAmbulanceAircrafts', function(source, cb)
    local ownedAmbulanceAircrafts = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.AdvVehShop then
        if Config.Main.ShowVehLoc then
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'aircraft',
                ['@job'] = 'ambulance'
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedAmbulanceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'aircraft' })
                end
                cb(ownedAmbulanceAircrafts)
            end)
        else
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'aircraft',
                ['@job'] = 'ambulance',
                ['@stored'] = true
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedAmbulanceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'aircraft' })
                end
                cb(ownedAmbulanceAircrafts)
            end)
        end
    else
        if Config.Main.ShowVehLoc then
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'helicopter',
                ['@job'] = 'ambulance'
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedAmbulanceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'helicopter' })
                end
                cb(ownedAmbulanceAircrafts)
            end)
        else
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'helicopter',
                ['@job'] = 'ambulance',
                ['@stored'] = true
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedAmbulanceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'helicopter' })
                end
                cb(ownedAmbulanceAircrafts)
            end)
        end
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedAmbulanceCars', function(source, cb)
    local ownedAmbulanceCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
        ['@owner'] = xPlayer.identifier,
        ['@job'] = 'ambulance',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedAmbulanceCars, vehicle)
        end
        cb(ownedAmbulanceCars)
    end)
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyAmbulance', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Ambulance.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payAmbulance')
AddEventHandler('tirc-garasi:payAmbulance', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Ambulance.PoundP)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. Config.Ambulance.PoundP })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Ambulance.PoundP)
        end)
    end
end)
-- End of Ambulance Code

-- Start of Police Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedPoliceCars', function(source, cb)
    local ownedPoliceCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'police'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedPoliceCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedPoliceCars)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'police',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedPoliceCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedPoliceCars)
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOwnedPoliceAircrafts', function(source, cb)
    local ownedPoliceAircrafts = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.AdvVehShop then
        if Config.Main.ShowVehLoc then
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'aircraft',
                ['@job'] = 'police'
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedPoliceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'aircraft' })
                end
                cb(ownedPoliceAircrafts)
            end)
        else
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'aircraft',
                ['@job'] = 'police',
                ['@stored'] = true
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedPoliceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'aircraft' })
                end
                cb(ownedPoliceAircrafts)
            end)
        end
    else
        if Config.Main.ShowVehLoc then
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'helicopter',
                ['@job'] = 'police'
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedPoliceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'helicopter' })
                end
                cb(ownedPoliceAircrafts)
            end)
        else
            MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
                ['@owner'] = xPlayer.identifier,
                ['@Type'] = 'helicopter',
                ['@job'] = 'police',
                ['@stored'] = true
            }, function(data)
                for _, v in pairs(data) do
                    local vehicle = json.decode(v.vehicle)
                    table.insert(ownedPoliceAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate, vtype = 'helicopter' })
                end
                cb(ownedPoliceAircrafts)
            end)
        end
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedPoliceCars', function(source, cb)
    local ownedPoliceCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
        ['@owner'] = xPlayer.identifier,
        ['@job'] = 'police',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedPoliceCars, vehicle)
        end
        cb(ownedPoliceCars)
    end)
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyPolice', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Police.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payPolice')
AddEventHandler('tirc-garasi:payPolice', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Police.PoundP)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. Config.Police.PoundP })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Police.PoundP)
        end)
    end
end)
-- End of Police Code

-- Start of Mechanic Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedMechanicCars', function(source, cb)
    local ownedMechanicCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'mechanic'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedMechanicCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedMechanicCars)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'mechanic',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedMechanicCars, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedMechanicCars)
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedMechanicCars', function(source, cb)
    local ownedMechanicCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
        ['@owner'] = xPlayer.identifier,
        ['@job'] = 'mechanic',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedMechanicCars, vehicle)
        end
        cb(ownedMechanicCars)
    end)
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyMechanic', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Mechanic.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payMechanic')
AddEventHandler('tirc-garasi:payMechanic', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Mechanic.PoundP)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. Config.Mechanic.PoundP })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Mechanic.PoundP)
        end)
    end
end)
-- End of Mechanic Code

-- Start Of Taxi
ESX.RegisterServerCallback('tirc-garasi:getOwnedTaxiCars', function(source, cb)
    local ownedMechanicCars = {} -- Seharusnya ownedTaxiCars agar konsisten
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'taxi'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedMechanicCars, { vehicle = vehicle, stored = v.stored, plate = v.plate }) -- Ganti ownedMechanicCars
            end
            cb(ownedMechanicCars) -- Ganti ownedMechanicCars
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@job'] = 'taxi',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedMechanicCars, { vehicle = vehicle, stored = v.stored, plate = v.plate }) -- Ganti ownedMechanicCars
            end
            cb(ownedMechanicCars) -- Ganti ownedMechanicCars
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedTaxiCars', function(source, cb)
    local ownedMechanicCars = {} -- Seharusnya ownedTaxiCars
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
        ['@owner'] = xPlayer.identifier,
        ['@job'] = 'taxi',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedMechanicCars, vehicle) -- Ganti ownedMechanicCars
        end
        cb(ownedMechanicCars) -- Ganti ownedMechanicCars
    end)
end)
-- End Of Taxi Code (ditambahkan komentar penutup)

-- Start of Aircraft Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedAircrafts', function(source, cb)
    local ownedAircrafts = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'aircraft',
            ['@job'] = 'civ'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedAircrafts)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'aircraft',
            ['@job'] = 'civ',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedAircrafts, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedAircrafts)
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedAircrafts', function(source, cb)
    local ownedAircrafts = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
        ['@owner'] = xPlayer.identifier,
        ['@Type'] = 'aircraft',
        ['@job'] = 'civ',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedAircrafts, vehicle)
        end
        cb(ownedAircrafts)
    end)
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyAircrafts', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Aircrafts.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payAircraft')
AddEventHandler('tirc-garasi:payAircraft', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Aircrafts.PoundP)

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Aircrafts.PoundP)
        end)
    end
end)
-- End of Aircraft Code

-- Start of Boat Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedBoats', function(source, cb)
    local ownedBoats = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if Config.Main.ShowVehLoc then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type', {
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'boat'
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedBoats, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedBoats)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND stored = @stored', {
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'boat',
            ['@stored'] = true
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedBoats, { vehicle = vehicle, stored = v.stored, plate = v.plate })
            end
            cb(ownedBoats)
        end)
    end
end)

ESX.RegisterServerCallback('tirc-garasi:getOutOwnedBoats', function(source, cb)
    local ownedBoats = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND job = @job AND `stored` = @stored', { -- job = NULL
        ['@owner'] = xPlayer.identifier,
        ['@Type'] = 'boat',
        ['@job'] = 'civ',
        ['@stored'] = false
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedBoats, vehicle)
        end
        cb(ownedBoats)
    end)
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyBoats', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Boats.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payBoat')
AddEventHandler('tirc-garasi:payBoat', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Boats.PoundP)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. Config.Boats.PoundP })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Boats.PoundP)
        end)
    end
end)
-- End of Boat Code

-- Start of Car Code
ESX.RegisterServerCallback('tirc-garasi:getOwnedCars', function(source, cb, garage)
    local xPlayer = ESX.GetPlayerFromId(source)
    local ownedCars = {}

    MySQL.Async.fetchAll([[
        SELECT * FROM owned_vehicles 
        WHERE (owner = @owner OR last_store_owner = @owner) 
        AND Type = @type AND garage = @garage AND stored = @stored
    ]], {
        ['@owner'] = xPlayer.identifier,
        ['@type'] = 'car',
        ['@garage'] = garage,
        ['@stored'] = 1
    }, function(data)
        for _, v in pairs(data) do
            local vehicle = json.decode(v.vehicle)
            table.insert(ownedCars, {
                vehicle = vehicle,
                stored = v.stored,
                plate = v.plate,
                garage = v.garage,
                vehiclename = v.vehiclename,
                owner = v.owner,
                last_store_owner = v.last_store_owner
            })
        end
        cb(ownedCars)
    end)
end)


ESX.RegisterServerCallback('tirc-garasi:getOutOwnedCars', function(source, cb, location)
    local ownedCars = {}
    local xPlayer = ESX.GetPlayerFromId(source)

    if location == 'Pound_Sandy' then
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND `stored` = @stored AND owned_vehicles.plate NOT IN (SELECT plate FROM h_impounded_vehicles)', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@stored'] = false
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedCars, { vehicle = vehicle, stored = v.stored, plate = v.plate, garage = v.garage, vehiclename = v.vehiclename })
            end
            cb(ownedCars)
        end)
    else
        MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored AND owned_vehicles.plate NOT IN (SELECT plate FROM h_impounded_vehicles)', { -- job = NULL
            ['@owner'] = xPlayer.identifier,
            ['@Type'] = 'car',
            ['@stored'] = false
        }, function(data)
            for _, v in pairs(data) do
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedCars, { vehicle = vehicle, stored = v.stored, plate = v.plate, garage = v.garage, vehiclename = v.vehiclename })
            end
            cb(ownedCars)
        end)
    end
end)

lib.callback.register('rnr_garage:Server:validateAndTakeVehicle', function(source, plate)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end
    if not plate or plate == '' then return false end

    local result = MySQL.query.await('SELECT * FROM owned_vehicles WHERE plate = ? AND stored = 1 AND Type = "car"', { plate })
    if not result or #result == 0 then
        return false
    end

    local veh = result[1]
    local vehicleData = json.decode(veh.vehicle)

    if veh.owner == xPlayer.identifier or veh.last_store_owner == xPlayer.identifier then
        return {
            model = vehicleData.model,
            vehicle = vehicleData,
            plate = veh.plate,
            owner = veh.owner,
            fuel = veh.fuelLevel
        }
    end

    return false
end)

ESX.RegisterServerCallback('checkMoney', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getMoney() >= 10000 then
        cb(true)
        xPlayer.removeMoney(10000)
    else
        cb(false)
    end
end)

ESX.RegisterServerCallback('ChangeNamePrice', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getMoney() >= 25000 then
        cb(true)
        xPlayer.removeMoney(25000)
    else
        cb(false)
    end
end)

RegisterServerEvent('garage:renamevehicle')
AddEventHandler('garage:renamevehicle', function(vehicleplate, name)
    MySQL.Async.execute("UPDATE owned_vehicles SET vehiclename = @vehiclename WHERE plate = @plate", { ['@vehiclename'] = name, ['@plate'] = vehicleplate })
end)

ESX.RegisterServerCallback('tirc-garasi:checkMoneyCars', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if xPlayer.getMoney() >= Config.Cars.PoundP then
        cb(true)
    else
        cb(false)
    end
end)

RegisterServerEvent('tirc-garasi:payCar')
AddEventHandler('tirc-garasi:payCar', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(Config.Cars.PoundP)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. ESX.Math.GroupDigits(Config.Cars.PoundP) })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(Config.Cars.PoundP)
        end)
    end
end)
-- End of Car Code

ESX.RegisterCommand('testquery', 'admin', function(xPlayer, args, showError)
    local start = os.clock()

    MySQL.Async.execute('UPDATE owned_vehicles SET stored = 1 WHERE plate = @plate', {
        ['@plate'] = 'TEST1234'
    }, function(rowsChanged)
        local finish = os.clock()
        -- print(('Query Time: %.4f ms'):format((finish - start) * 1000))
    end)
end, true, { help = "Test Query Speed" })

-- Store Vehicles
local webhookURL = "https://discord.com/api/webhooks/1377709461531000912/ryGJiDykdTs-caehbxreM_0kXWGNLmgG4i9wDHe1QUmQWenwAycKUW5Sur9ecelgbYZt" -- Ganti dengan webhook kamu

local function sendDiscordLog(title, description, color)
    PerformHttpRequest(webhookURL, function(err, text, headers) end, "POST", json.encode({
        embeds = {{
            title = title,
            description = description,
            color = color,
            footer = {
                text = os.date("%Y-%m-%d %H:%M:%S")
            }
        }}
    }), { ["Content-Type"] = "application/json" })
end

lib.callback.register('tirc-garasi:storeVehicle', function(source, vehicleProps, garage)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end

    local plate = vehicleProps.plate:match("^%s*(.-)%s*$")
    local result = MySQL.query.await('SELECT owner FROM owned_vehicles WHERE plate = ?', { plate })

    if not result or not result[1] then
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = 'Kendaraan tidak ditemukan dalam database!',
            duration = 5000
        })
        return false
    end

    local vehicleOwner = result[1].owner
    if vehicleOwner ~= xPlayer.identifier then
        local desc = ("**Pemain:** `%s`\n**Plate:** `%s`\n**Menyimpan kendaraan milik:** `%s`\n**Garasi:** `%s`")
            :format(xPlayer.identifier, plate, vehicleOwner, garage)
        sendDiscordLog("🚨 [GARASI WARNING] Pemain menyimpan kendaraan milik orang lain", desc, 16753920) -- merah
    end

    local update = MySQL.update.await([[
        UPDATE owned_vehicles 
        SET vehicle = ?, garage = ?, stored = 1, last_store_owner = ?
        WHERE plate = ?
    ]], {
        json.encode(vehicleProps),
        garage,
        xPlayer.identifier,
        plate
    })

    if update > 0 then
        local desc = ("**Pemain:** `%s`\n**Plate:** `%s`\n**Garasi:** `%s`")
            :format(xPlayer.identifier, plate, garage)
        sendDiscordLog("✅ [GARASI] Kendaraan berhasil disimpan", desc, 5763719) -- hijau
        return true
    else
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = 'Gagal menyimpan kendaraan!',
            duration = 5000
        })
        return false
    end
end)

-- Pay to Return Broken Vehicles
RegisterServerEvent('tirc-garasi:payhealth')
AddEventHandler('tirc-garasi:payhealth', function(price)
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeMoney(price)
    TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = _U('you_paid') .. price })

    if Config.Main.GiveSocMoney then
        TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mechanic', function(account)
            account.addMoney(price)
        end)
    end
end)

-- Modify State of Vehicles
RegisterServerEvent('tirc-garasi:setVehicleState')
AddEventHandler('tirc-garasi:setVehicleState', function(plate, state)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = @stored WHERE plate = @plate', {
        ['@stored'] = state,
        ['@plate'] = plate
    }, function(rowsChanged)
        if rowsChanged == 0 then
            -- print(('tirc-garasi: %s Exploited The Garage!'):format(xPlayer.identifier))
        end
    end)
end)

RegisterServerEvent('vehiclesStored')
AddEventHandler('vehiclesStored', function(plate, vehprop)
    local xPlayer = ESX.GetPlayerFromId(source)

    MySQL.Async.execute('UPDATE owned_vehicles SET vehicle = @vehicle WHERE plate = @plate', {
        ['@vehicle'] = json.encode(vehprop),
        ['@plate'] = plate
    }, function(rowsChanged)
        if rowsChanged == 0 then
            print(('esx_advancedgarage: %s exploited the garage!'):format(xPlayer.identifier))
        end
    end)
end)