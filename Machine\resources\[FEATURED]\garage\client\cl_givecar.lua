ESX = exports["r_core"]:getSharedObject()

lib.callback.register('rise-givecar:client:vehicleData', function(model, plate, playerName)
    local promise = promise.new()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local function generatePlate()
    local letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    local numbers = '0123456789'

    local function randChar(str)
        local i = math.random(1, #str)
        return str:sub(i, i)
    end

    local plate = ''
    plate = plate .. randChar(letters)
    plate = plate .. randChar(numbers)
    plate = plate .. randChar(numbers)
    plate = plate .. randChar(letters)

        return plate
    end


    local finalPlate = plate and string.upper(plate) or generatePlate()

    -- Cek apakah plate sudah digunakan
    ESX.TriggerServerCallback('rise-givecar:isPlateTaken', function(isTaken)
        if isTaken then
            ESX.ShowNotification('Plat ini sudah digunakan oleh kendaraan lain.')
            return promise:resolve(nil)
        end

        ESX.Game.SpawnVehicle(model, coords, 0.0, function(vehicle)
            if not DoesEntityExist(vehicle) then
                ESX.ShowNotification('Gagal spawn kendaraan.')
                return promise:resolve(nil)
            end

            SetEntityVisible(vehicle, false)
            SetEntityCollision(vehicle, false)

            local vehProps = ESX.Game.GetVehicleProperties(vehicle)
            vehProps.plate = finalPlate

            ESX.Game.DeleteVehicle(vehicle)

            ESX.ShowNotification(('Kendaraan dengan plat nomor %s telah diparkir di garasi %s'):format(finalPlate, playerName))
            return promise:resolve(vehProps)
        end)
    end, finalPlate)

    return Citizen.Await(promise)
end)

local function getLabelVehicleTF(model)
    local NamaKendaraan = GetLabelText(GetDisplayNameFromVehicleModel(model))
    if NamaKendaraan == 'NULL' then
        NamaKendaraan = GetDisplayNameFromVehicleModel(model)
    end
    return NamaKendaraan
end

RegisterCommand("cekcar", function(_, args)
    local identifier = args[1]
    if not identifier or not identifier:match("^steam:") then
        lib.notify({ description = "Gunakan: /cekcar steam:hex", type = 'error' })
        return
    end

    lib.callback('admin:canUseCommand', false, function(allowed)
        if not allowed then
            lib.notify({ description = "Kamu tidak punya izin untuk pakai perintah ini!", type = 'error' })
            return
        end

        showVehicleMenu(identifier)
    end)
end)

-- Fungsi untuk ambil label readable dari model kendaraan
local function getLabelVehicleTF(model)
    local NamaKendaraan = GetLabelText(GetDisplayNameFromVehicleModel(model))
    if NamaKendaraan == 'NULL' then
        NamaKendaraan = GetDisplayNameFromVehicleModel(model)
    end
    return NamaKendaraan
end

-- Fungsi utama menu kendaraan
function showVehicleMenu(identifier)
    lib.callback('admin:getVehiclesByIdentifier', false, function(vehicles)
        if not vehicles or #vehicles == 0 then
            lib.notify({ description = 'Player tidak punya kendaraan.', type = 'info' })
            return
        end

        local options = {}

        for _, v in ipairs(vehicles) do
            local dataveh = json.decode(v.vehicle)
            local modelLabel = getLabelVehicleTF(dataveh.model)

            table.insert(options, {
                title = v.plate .. " - " .. modelLabel,
                icon = "car",
                onSelect = function()
                    local garageOptions = {}
                    for k, garage in pairs(Config.CarGarages) do
                        table.insert(garageOptions, {
                            title = garage.Name,
                            icon = 'warehouse',
                            onSelect = function()
                                TriggerServerEvent('admin:moveVehicleToGarage', v.plate, k)
                                lib.notify({ description = 'Kendaraan dipindahkan ke garasi ' .. garage.Name, type = 'success' })
                            end
                        })
                    end

                    lib.registerContext({
                        id = 'admin_vehicle_action_' .. v.plate,
                        title = 'Kendaraan: ' .. v.plate .. " - " .. modelLabel,
                        options = {
                            {
                                title = 'Ganti Plat',
                                icon = 'pen',
                                onSelect = function()
                                    local input = lib.inputDialog('Ganti Plat', { 'Plat Baru' })
                                    if input and input[1] and input[1] ~= '' then
                                        TriggerServerEvent('admin:updateVehiclePlate', v.plate, input[1])
                                        lib.notify({ description = 'Plat berhasil diganti!', type = 'success' })
                                    end
                                end
                            },
                            {
                                title = 'Hapus Kendaraan',
                                icon = 'trash',
                                onSelect = function()
                                    TriggerServerEvent('admin:deleteVehicle', v.plate)
                                    lib.notify({ description = 'Kendaraan berhasil dihapus!', type = 'success' })
                                end
                            },
                            {
                                title = 'Pindahkan ke Garasi',
                                icon = 'warehouse',
                                onSelect = function()
                                    lib.registerContext({
                                        id = 'admin_move_garage_' .. v.plate,
                                        title = 'Pilih Garasi',
                                        options = garageOptions
                                    })
                                    lib.showContext('admin_move_garage_' .. v.plate)
                                end
                            }
                        }
                    })

                    lib.showContext('admin_vehicle_action_' .. v.plate)
                end
            })
        end

        lib.registerContext({
            id = "admin_vehicle_list",
            title = "Kendaraan Player",
            options = options
        })

        lib.showContext("admin_vehicle_list")
    end, identifier)
end


RegisterCommand("admincar", function()
    local playerPed = PlayerPedId()
    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        local plate = ESX.Math.Trim(GetVehicleNumberPlateText(vehicle))
        TriggerServerEvent("admincar:claimOwnership", plate)
        ESX.ShowNotification("~g~Kendaraan ini telah menjadi milikmu.")
    else
        ESX.ShowNotification("~r~Kamu tidak berada di dalam kendaraan.")
    end
end, false)