{"clothing_interact_label": "Interact", "armory_interact_label": "Get equipment", "armory_interact_storage_label": "Open storage", "already_have_item": "You already have this item", "no_item_in_storage": "is out of stock", "search_suspect_label": "Search suspect", "handcuff_suspect_label": "Handcuff suspect", "uncuff_suspect_label": "Uncu<PERSON> suspect", "drag_suspect_label": "Drag suspect", "put_suspect_in_vehicle_label": "Put suspect in vehicle", "take_suspect_out_vehicle_label": "Take suspect out from vehicle", "stop_dragging_msg": "[BACKSPACE] - To Stop Dragging", "fine_suspect_label": "Fine Suspect", "fine_dialog_title": "Fine Menu", "fine_amount_title": "Fine Amount", "fine_reason_title": "Fine reason", "have_been_fined": "You have been fined for %s for %s", "arrest_suspect_label": "Arrest Suspect", "garage_interact_label": "Open garage", "deposit_vehicle": "[E] - deposit vehicle", "open_stash_label": "[E] - Open stash", "restrict_area_dialog_title": "Restrict Area", "restrict_area_message_title": "Message", "restrict_area_message_description": "The message to people", "restrict_area_radius_title": "<PERSON><PERSON>", "restrict_area_radius_description": "Area to interdict", "restrict_area_time_title": "Time", "restrict_area_time_description": "Time to rescrict the area es. 5", "restrict_area_type_title": "Type", "restrict_area_type_description": "Type of interdict", "restrict_area_type_1": "Shooting", "restrict_area_type_2": "<PERSON><PERSON>", "restrict_area_type_3": "Presidential security", "placing_prop_label": "Placing prop", "picking_up_prop_label": "Picking up object", "take_cone_label": "Cone", "take_barrier_label": "Barrier", "take_spikestrips_label": "Spikestrips", "placing_prop_instructions": "ㅤㅤㅤㅤㅤ**CONTROLS**ㅤㅤㅤㅤㅤ  \n [ENTER] to place  \n [BACKSPACE] to cancel  \n [E] to rotate left  \n [Q] to rotate right", "view_cameras_label": "View Camera", "camera_instructions": "ㅤㅤㅤㅤㅤ**CONTROLS**ㅤㅤㅤㅤㅤ  \n [E / Q] to swtich camera   \n [A] to rotate left  \n [D] to rotate right  \n [SCROLL UP] to zooom in  \n [SCROLLL DOWN] to zoom out  \n [BACKSPACE] to exit   \n [CURRENT CAM] %s", "stop_dragging": "[BACKSPACE] to stop dragging ", "bossmenu_label": "Open bossmenu ", "emergency_blip_label": "Help", "adam_notif_label": "<PERSON>: ", "status_notif_label": "Status: ", "location_notif_label": "Location: ", "status_notif_title": "📢 LSPD NOTIFICATION 📢", "your_off_duty": "Your not in duty", "call_metting_label": "Call meeting", "set_adam_label": "Set Your Adam", "set_adam_label_status": "Set Your Adam Status", "adam_receive_notif_label": "Receive status notifications", "adam_receive_notif_dec": "Enable if to receive other police officer status notifications", "meeting_reason_label": "Meeting Reason:", "read_meeting_reason_label": "Read the reason under", "fz_metting": "Meeting in Fz: ", "join_fz": "Join frequncy", "open_called_meeting_menu": "A high grade called a meeting Press [U] for more info", "no_adam_set": "No adam set", "police_menu_title": "👮‍♂️ Police Menu 👮‍♂️", "open_adam_menu_label": "<PERSON>", "open_adam_menu_description": "Adam settings", "open_meeting_menu_label": "Call Meeting", "meeting_input_reason": "Reason", "meeting_input_radio": "Radio Fz", "meeting_input_confirmation": "Confirm", "meeting_input_reason_desc": "Reason for the meeting", "meeting_input_radio_desc": "Radio frequency that all will join", "police_meeting_menu_title": "👮‍♂️ Meeting Info 👮‍♂️", "adam_menu_title": "👮‍♂️ Adam <PERSON>u 👮‍♂️", "input_set_adam_title": "🚓 LSPD", "input_enter_adam_num": "Enter Your Adam Number", "input_enter_adam_num_desc": "Enter the number of your assigned adam", "your_in_adam_x": "You are now in adam", "adam_status_menu_title": "🚨 Set Your Adam Status 🚨", "adam_status_changed": "You changed your status", "clock_in": "On Duty", "clock_out": "Off Duty", "clocked_in": "You clocked in for the job", "already_in_duty": "You are already clocked in", "clocked_out": "You clocked out", "already_off_duty": "Your already clocked out", "broadcast_alert_label": "Broadcast message to everyone", "broadcast_alert_label_dsc": "", "broadcast_input_title": "Broadcast", "broadcast_input_reason": "Message", "broadcast_input_reason_desc": "the message that everyone will see", "broadcast_notif_title": "📢 LSPD NOTIFICATION 📢", "radial-garage": "Garage", "radial-access-store": "Store Vehicle", "radial-access-garage": "View Vehicles", "radial-access-garage-job": "Gara<PERSON>", "radial-access-impound": "Impounded Vehicles", "car_garage_blip": "Garage", "plane_garage_blip": "Aircraft Garage", "boat_garage_blip": "Boat Garage", "impound_blip": "Impounded Vehicles", "police_garage_blip": "Police Garage", "ambulance_garage_blip": "Ambulance Garage", "access-store": "[E] - Store Vehicle", "access-garage": "[E] - View Stored Vehicles", "access-garage-job": "[E] - <PERSON> Garage <PERSON>", "access-impound": "[E] - View Impounded Vehicles", "elgin": "Central Garage", "aguja": "Aguja Street Garage", "great_ocean": "Great Ocean Garage", "olympic": "Olympic Garage", "shambles": "Shambles Garage", "panorama_drive": "Panorama Drive Garage", "new_empire": "New Empire Garage", "vespucci_police": "Vespucci Police Garage", "strawberry_ambulance": "Strawberry EMS Garage", "innocence": "Innocence Boulevard Impound", "vespucci": "Vespucci Boulevard Impound", "paleto": "Paleto Impound", "zancudo": "Zancudo Avenue Impound", "pista_1": "Pista 1 Impound", "plate": "Plate: %s", "fuel": "Fuel", "engine": "Engine", "job": "(Job)", "buy": "Buy for $%s", "parked_in": "Parked in:", "impounded": "(Impounded)", "take_out_vehicle": "Take vehicle out of garage", "transfer_vehicle": "Transfer vehicle to this garage ($%s)", "garage_shop": "Vehicle Shop", "stored_vehicles": "Stored Vehicles", "recover_vehicle": "Recover Vehicle ($%s)", "send_vehicle_to_impound": "Send Vehicle to Impound", "find_vehicle": "Find Vehicle", "no_vehicles_found": "You have no vehicles", "vehicle_moved": "Vehicle moved to this garage", "not_enought_money": "You don't have enough money.", "vehicle_out": "Vehicle is out of the garage.", "vehicle_stored": "Vehicle stored successfully.", "vehicle_impounded": "Impounded vehicle.", "vehicle_sent_to_impounded": "Vehicle sent to impound.", "vehicle_found": "Vehicle marked on your GPS.", "vehicle_purchased": "Vehicle purchased.", "not_in_vehicle": "You are not in a vehicle.", "vehicle_not_found": "Vehicle not found.", "vehicle_not_allowed": "You can't store this vehicle here.", "not_owner": "You don't own this vehicle.", "no_vehicles_nearby": "No vehicles nearby.", "impounding_progress": "Impounding vehicle...", "command_impound": "Vehicle impound"}