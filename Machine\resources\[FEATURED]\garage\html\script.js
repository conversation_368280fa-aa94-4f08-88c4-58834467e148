var selected = null;
var state = false;
var allowedOut = false;
var status = null;
function clearSelectedList(){
    $('.info').html('');
    $(".list").each(function(){
        $(this).attr("class", "list");
    });
}
function clearSelectedListallowed(){
    $('.info').html('');
    $(".listallowed").each(function(){
        $(this).attr("class", "listallowed");
    });
}
function clearSelectedListdisallowed(){
    $('.info').html('');
    $(".listdisallowed").each(function(){
        $(this).attr("class", "listdisallowed");
    });
}

function formPopUp(vehProp, status, location, vehiclename, price){
    $('#ContainerTwo').show();
    let Pricenum = 0;
    if(price === undefined){
        Pricenum = 0;
    }else
    {
        Pricenum = (price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    }

    //$('.info').append('Plate : ' + vehProp.plate + ' <br> Model : ' +vehiclename+ '<br> Status : '  + status + ' <br> Location : ' +location+ ' <br>Body Health : ' + (vehProp.bodyHealth/1000 * 100).toFixed(2) + '% <br> Engine Health : ' + (vehProp.engineHealth/1000 * 100).toFixed(2) + '%' )
    $('.info').append('Plate : ' + vehProp.plate + ' <br> Model : ' +vehiclename+ '<br>  Location : ' +location+ ' <br>Body Health : ' + (vehProp.bodyHealth/1000 * 100).toFixed(2) + '% <br> Engine Health : ' + (vehProp.engineHealth/1000 * 100).toFixed(2) + '%  <br> Biaya Impound : $ ' + Pricenum + '' )
}

// TODO : FUNCTION SEARCH
// function searchveh(value, name) {
//     //var input = document.getElementsByClassName("searchvehicles");
//     var filter = value.toLowerCase();
//     var nodes = document.getElementsByClassName(name);
//
//     for (i = 0; i < nodes.length; i++) {
//         if (nodes[i].innerText.toLowerCase().includes(filter)) {
//             nodes[i].style.display = "block";
//             nodes[i].style.className = name;
//         } else {
//             nodes[i].style.display = "none";
//             nodes[i].style.className = name;
//         }
//     }
// }

function searchveh(value, name) {
    filter = value.toLowerCase();
    //Show all div class target
    $("div."+name).show();
    //All div class target that not contains filter will be hidden
    $("div."+name).each(function(index, elem){
        if($(elem).text().toLowerCase().includes(filter)) { //Not hidden
        } else { $(elem).hide(); }
    });
}

$(document).ready(function() {
    window.addEventListener('message', function (event) {
        data = event.data;
        action = event.data.action;
        if(action === 'openVehiclesList'){
            $('#ContainerOne').show();
            $('#garages').show();
            $('#pub_impound').hide();
            $('#police_imp').hide();
            $('#trunk_view').show();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').show();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            var data = event.data.data;
            for (let i = 0; i < data.length; i++){
                if (data[i].allowed === true) {
                    $('.Wrap').append('<div class=\'listallowed\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\'>\n' +
                        '                    '+ data[i].vehicleName + ' - ' + data[i].plate +
                        '                </div>')
                }else{
                    $('.Wrap').append('<div class=\'listdisallowed\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\'>\n' +
                        '                    '+ data[i].vehicleName + ' - ' + data[i].plate +
                        '                </div>')
                }
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'listallowed');
                searchveh($(this).val(), 'listdisallowed');
            });
        }
        if(action === 'openVehiclesListImpound'){
            $('#ContainerOne').show();
            $('#pub_impound').show();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            var harga = 10000;
            var data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat7=\''+harga+'\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\'>\n' +
                    '                    '+ data[i].vehicleName + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }
        if(action === 'openVehiclesListPoliceImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').show();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'openVehiclesListKSPImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').show();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'openVehiclesListSPImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').show();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'openVehiclesListVGImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').hide();
            $('#vg_imp').show();
            $('#mc_imp').hide();
            $('#ems_imp').hide();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'openVehiclesListMCImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').show();
            $('#ems_imp').hide();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'openVehiclesListEMSImp'){
            $('#ContainerOne').show();
            $('#pub_impound').hide();
            $('#garages').hide();
            $('#police_imp').hide();
            $('#trunk_view').hide();
            $('.searchvehicles').val('');
            $('.searchvehicles').focus();
            $('#ksp_imp').hide();
            $('#sp_imp').hide();
            $('#vg_imp').hide();
            $('#mc_imp').hide();
            $('#ems_imp').show();
            let data = event.data.data;
            for (let i = 0; i < data.length; i++){
                $('.Wrap').append('<div class=\'list\' data-cat=\''+data[i].status+'\' data-cat2=\''+data[i].VehProp+'\' data-cat3=\''+data[i].state+'\' data-cat4=\''+data[i].location+'\' data-cat5=\''+data[i].allowed+'\' data-cat6=\''+data[i].vehicleName+'\' >\n' +
                    '                    '+ data[i].owner + ' - ' + data[i].plate +
                    '                </div>')
            }
            $('.searchvehicles').on('keyup keydown', function(e) {
                searchveh($(this).val(), 'list');
            });
        }

        if(action === 'ShowTrunkInv'){
            $('#ContainerThree').show();
            $('.info2').html('');
            var data = event.data.data;
            $.each(data, function (index, item) {
                $('.info2').append('<div class="slot1"><div class="item-key"> '+ item.count +' </div><div class="item" data-cat=" '+item.name+' " style = "background-image: url(\'img/items/' + item.name + '.png\')">' +
                    '<div class="item-name">' + item.name + '</div> </div ><div class="item-name-bg"></div></div>');
            });
        }

        if(action === 'CloseVehiclesList'){
            $('#ContainerOne').hide();
            $('#ContainerTwo').hide();
            $('#ContainerThree').hide();
            $('.wrap').html('');
        }
    });

    $(document).on("click", ".list", function(){
        $('#ContainerThree').hide();
        status = $(this).data("cat");
        selected = $(this).data("cat2");
        state = $(this).data("cat3");
        var location = $(this).data("cat4");
        allowedOut = $(this).data("cat5");
        var vehicleName = $(this).data("cat6");
        var price = $(this).data("cat7");
        clearSelectedList();
        $(this).attr("class", "list activecat");
        formPopUp(selected, status, location, vehicleName, price);

    });
    $(document).on("click", ".listallowed", function(){
        $('#ContainerThree').hide();
        status = $(this).data("cat");
        selected = $(this).data("cat2");
        state = $(this).data("cat3");
        var location = $(this).data("cat4");
        allowedOut = $(this).data("cat5");
        var vehicleName = $(this).data("cat6");
        clearSelectedListallowed();
        clearSelectedListdisallowed();
        $(this).attr("class", "listallowed activecat");
        formPopUp(selected, status, location, vehicleName);

    });

    $(document).on("click", ".listdisallowed", function(){
        $('#ContainerThree').hide();
        status = $(this).data("cat");
        selected = $(this).data("cat2");
        state = $(this).data("cat3");
        var location = $(this).data("cat4");
        allowedOut = $(this).data("cat5");
        var vehicleName = $(this).data("cat6");
        clearSelectedListallowed();
        clearSelectedListdisallowed();
        $(this).attr("class", "listdisallowed activecat");
        formPopUp(selected, status, location, vehicleName);

    });

    $(document).on("mouseenter", ".listallowed", function(){
        if ($(this).attr("class") != "listallowed activecat")
        {
            if ($(this).attr("class") != "listallowed selected")
            {
                $(this).attr("class", "listallowed selected");
            }
        }
    });
    $(document).on("mouseleave", ".listallowed", function(){
        if ($(this).attr("class") != "listallowed activecat")
        {
            if ($(this).attr("class") != "listallowed")
            {
                $(this).attr("class", "listallowed");
            }
        }
    });

    $(document).on("mouseenter", ".listdisallowed", function(){
        if ($(this).attr("class") != "listdisallowed activecat")
        {
            if ($(this).attr("class") != "listdisallowed selected")
            {
                $(this).attr("class", "listdisallowed selected");
            }
        }
    });
    $(document).on("mouseleave", ".listdisallowed", function(){
        if ($(this).attr("class") != "listdisallowed activecat")
        {
            if ($(this).attr("class") != "listdisallowed")
            {
                $(this).attr("class", "listdisallowed");
            }
        }
    });


    $(document).on("mouseenter", ".list", function(){
        if ($(this).attr("class") != "list activecat")
        {
            if ($(this).attr("class") != "list selected")
            {
                $(this).attr("class", "list selected");
            }
        }
    });
    $(document).on("mouseleave", ".list", function(){
        if ($(this).attr("class") != "list activecat")
        {
            if ($(this).attr("class") != "list")
            {
                $(this).attr("class", "list");
            }
        }
    });


    $(".takeout").mouseenter(function(){
        if ($(this).attr("class") != "takeout activecat")
        {
            if ($(this).attr("class") != "takeout selected")
            {
                $(this).attr("class", "takeout selected");
            }
        }
    });
    $(".takeout").mouseleave(function(){
        if ($(this).attr("class") != "takeout activecat")
        {
            if ($(this).attr("class") != "takeout")
            {
                $(this).attr("class", "takeout");
            }
        }
    });
    $(".takeout").click(function(){
        if (state === true && allowedOut === true && status === 'Stored') {
            $.post("http://garage/takeout", JSON.stringify({selected: selected}));
        }else if(state === true && allowedOut === false && status === 'Stored'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : 'different_garage' }));
        }else if(state === false && allowedOut === true && status === 'Exiting'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : 'exit' }));
        }else if(state === false && allowedOut === false && status === 'Impound') {
            $.post("http://garage/takeout", JSON.stringify({selected: 'impounded'}));
        }else if(state === true && allowedOut === true && status === 'vvip'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : selected , from : 'vvip'}));
        }else if(state === false && allowedOut === false && status === 'Exiting'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : 'exit' }));
        }else if(state === false && allowedOut === false && status === 'Permai'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : 'permai' }));
        }else if(state === false && allowedOut === false && status === 'Guarantee KSP'){
            $.post("http://garage/takeout" , JSON.stringify({ selected : 'ksp' }));
        }else if(state === false && allowedOut === false && status === 'Guarantee Speed Garage') {
            $.post("http://garage/takeout", JSON.stringify({selected: 'sp'}));
        }else if(state === false && allowedOut === false && status === 'Guarantee VIP Garage') {
            $.post("http://garage/takeout", JSON.stringify({selected: 'vg' }));
        }else if(state === false && allowedOut === false && status === 'Guarantee Sport Race Garage') {
            $.post("http://garage/takeout", JSON.stringify({selected: 'mc' }));
        }else if(state === false && allowedOut === false && status === 'Guarantee Hospital Garage') {
            $.post("http://garage/takeout", JSON.stringify({selected: 'ems' }));
        }
    });

    $(".takeout2").mouseenter(function(){
        if ($(this).attr("class") != "takeout2 activecat")
        {
            if ($(this).attr("class") != "takeout2 selected")
            {
                $(this).attr("class", "takeout2 selected");
            }
        }
    });
    $(".takeout2").mouseleave(function(){
        if ($(this).attr("class") != "takeout2 activecat")
        {
            if ($(this).attr("class") != "takeout2")
            {
                $(this).attr("class", "takeout2");
            }
        }
    });
    $(".takeout2").click(function(){
        $.post("http://garage/takeout2" , JSON.stringify({ selected : selected }));
    });

    $(".takeout3").mouseenter(function(){
        if ($(this).attr("class") != "takeout3 activecat")
        {
            if ($(this).attr("class") != "takeout3 selected")
            {
                $(this).attr("class", "takeout3 selected");
            }
        }
    });
    $(".takeout3").mouseleave(function(){
        if ($(this).attr("class") != "takeout3 activecat")
        {
            if ($(this).attr("class") != "takeout3")
            {
                $(this).attr("class", "takeout3");
            }
        }
    });
    $(".takeout3").click(function(){
        $.post("http://garage/takeout3" , JSON.stringify({ selected : selected }));
    });

    $(".takeout4").mouseenter(function(){
        if ($(this).attr("class") != "takeout4 activecat")
        {
            if ($(this).attr("class") != "takeout4 selected")
            {
                $(this).attr("class", "takeout4 selected");
            }
        }
    });
    $(".takeout4").mouseleave(function(){
        if ($(this).attr("class") != "takeout4 activecat")
        {
            if ($(this).attr("class") != "takeout4")
            {
                $(this).attr("class", "takeout4");
            }
        }
    });
    $(".takeout4").click(function(){
        $.post("http://garage/takeout4" , JSON.stringify({ selected : selected }));
    });

    $(".takeout5").mouseenter(function(){
        if ($(this).attr("class") != "takeout5 activecat")
        {
            if ($(this).attr("class") != "takeout5 selected")
            {
                $(this).attr("class", "takeout5 selected");
            }
        }
    });
    $(".takeout5").mouseleave(function(){
        if ($(this).attr("class") != "takeout5 activecat")
        {
            if ($(this).attr("class") != "takeout5")
            {
                $(this).attr("class", "takeout5");
            }
        }
    });
    $(".takeout5").click(function(){
        $.post("http://garage/takeout5" , JSON.stringify({ selected : selected }));
    });

    $(".takeout6").mouseenter(function(){
        if ($(this).attr("class") != "takeout6 activecat")
        {
            if ($(this).attr("class") != "takeout6 selected")
            {
                $(this).attr("class", "takeout6 selected");
            }
        }
    });
    $(".takeout6").mouseleave(function(){
        if ($(this).attr("class") != "takeout6 activecat")
        {
            if ($(this).attr("class") != "takeout6")
            {
                $(this).attr("class", "takeout6");
            }
        }
    });
    $(".takeout6").click(function(){
        $.post("http://garage/takeout6" , JSON.stringify({ selected : selected }));
    });

    $(".takeout7").mouseenter(function(){
        if ($(this).attr("class") != "takeout7 activecat")
        {
            if ($(this).attr("class") != "takeout7 selected")
            {
                $(this).attr("class", "takeout7 selected");
            }
        }
    });
    $(".takeout7").mouseleave(function(){
        if ($(this).attr("class") != "takeout7 activecat")
        {
            if ($(this).attr("class") != "takeout7")
            {
                $(this).attr("class", "takeout7");
            }
        }
    });
    $(".takeout7").click(function(){
        $.post("http://garage/takeout7" , JSON.stringify({ selected : selected }));
    });

    $(".takeout8").mouseenter(function(){
        if ($(this).attr("class") != "takeout8 activecat")
        {
            if ($(this).attr("class") != "takeout8 selected")
            {
                $(this).attr("class", "takeout8 selected");
            }
        }
    });
    $(".takeout8").mouseleave(function(){
        if ($(this).attr("class") != "takeout8 activecat")
        {
            if ($(this).attr("class") != "takeout8")
            {
                $(this).attr("class", "takeout8");
            }
        }
    });
    $(".takeout8").click(function(){
        $.post("http://garage/takeout8" , JSON.stringify({ selected : selected }));
    });

    $(".trunk_check").mouseenter(function(){
        if ($(this).attr("class") != "trunk_check activecat")
        {
            if ($(this).attr("class") != "trunk_check selected")
            {
                $(this).attr("class", "trunk_check selected");
            }
        }
    });
    $(".trunk_check").mouseleave(function(){
        if ($(this).attr("class") != "trunk_check activecat")
        {
            if ($(this).attr("class") != "trunk_check")
            {
                $(this).attr("class", "trunk_check");
            }
        }
    });
    $(".trunk_check").click(function(){
        $.post("http://garage/trunk_check" , JSON.stringify({ selected : selected }));
    });

    $(".exitBtn").mouseenter(function(){
        if ($(this).attr("class") != "exitBtn activecat")
        {
            if ($(this).attr("class") != "exitBtn selected")
            {
                $(this).attr("class", "exitBtn selected");
            }
        }
    });
    $(".exitBtn").mouseleave(function(){
        if ($(this).attr("class") != "exitBtn activecat")
        {
            if ($(this).attr("class") != "exitBtn")
            {
                $(this).attr("class", "exitBtn");
            }
        }
    });
    $(".exitBtn").click(function(){
        $.post("http://garage/ExitBtn" , JSON.stringify({}));
    });
});



$(document).keydown(function(e) {
    // ESCAPE key pressed
    if (e.keyCode == 27) {
        $.post("http://garage/ExitBtn" , JSON.stringify({}));
    }
});