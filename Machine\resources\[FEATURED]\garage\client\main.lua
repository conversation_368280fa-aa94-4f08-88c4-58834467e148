local this_Garage, vehInstance, BlipList, PrivateBlips, JobBlips = {}, {}, {}, {}, {}
local WasInPound, WasinJPound = false, false
local AdaDiGarasi = false
local bukaumum = false
local ESX = exports["r_core"]:getSharedObject()
local takenOutVehicles = {}

Citizen.CreateThread(function()

	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end

	ESX.PlayerData = ESX.GetPlayerData()

	refreshBlips()
	RefreshJobBlips()
end)

-- local function reformatTable(t)
--     local newTable = {}
--     for k, v in pairs(t) do
--         newTable[v] = true
--     end
--     return newTable
-- end

-- local function checkAccess(access, groups, xPlayer)
--     local PlayerData = xPlayer or ESX.GetPlayerData()
--     local PlayerIdentifier = {PlayerData.identifier, PlayerData.citizenid, PlayerData.group}

--     if access then
--         if type(access) == 'string' then
--             return lib.table.contains(PlayerIdentifier, access)
--         elseif type(access) == 'table' and table.type(access) ~= 'empty' then
--             local tabs = reformatTable(access)
--             for k, v in ipairs(PlayerIdentifier) do
--                 if tabs[v] then return true end
--             end
--         end
--     end

--     if groups then
--         if type(groups) == 'string' then
--             return groups == PlayerData.job.name
--         elseif type(groups) == 'table' and table.type(groups) ~= 'empty' then
--             return groups[PlayerData.job.name] and PlayerData.job.grade >= groups[PlayerData.job.name]
--         end
--     end
--     return false
-- end

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	if Config.Pvt.Garages then
		ESX.TriggerServerCallback('tirc-garasi:getOwnedProperties', function(properties)
			userProperties = properties
			DeletePrivateBlips()
			RefreshPrivateBlips()
		end)
	end

	ESX.PlayerData = xPlayer

	RefreshJobBlips()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    ESX.PlayerData.job = job

	DeleteJobBlips()
	RefreshJobBlips()
end)

RegisterNetEvent('tirc-garasi:getPropertiesC')
AddEventHandler('tirc-garasi:getPropertiesC', function(xPlayer)
	if Config.Pvt.Garages then
		ESX.TriggerServerCallback('tirc-garasi:getOwnedProperties', function(properties)
			userProperties = properties
			DeletePrivateBlips()
			RefreshPrivateBlips()
		end)
		exports['ox_lib']:SendAlert('inform', _U('get_properties'))
		TriggerServerEvent('tirc-garasi:printGetProperties')
	end
end)

local function has_value (tab, val)
	for index, value in ipairs(tab) do
		if value == val then
			return true
		end
	end
	return false
end

-- Start of Ambulance Code
function ListOwnedAmbulanceMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - <span style="color:red;">%s</span> |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {label = spacer, value = nil})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {label = ('<span style="color:red;">%s</span>'):format(_U('spacer1')), value = nil})
		table.insert(elements, {label = spacer, value = nil})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedAmbulanceCars', function(ownedAmbulanceCars)
		if #ownedAmbulanceCars == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_ambulance'))
		else
			for _,v in pairs(ownedAmbulanceCars) do
				local hashVehicule = v.vehicle.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
				local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

				if Config.Main.ShowVehLoc then
					if v.stored then
						labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
					else
						labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
					end
				else
					if v.stored then
						labelvehicle = labelvehicle3
					else
						labelvehicle = labelvehicle3
					end
				end

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		table.insert(elements, {label = _U('spacer2'), value = nil})

		ESX.TriggerServerCallback('tirc-garasi:getOwnedAmbulanceAircrafts', function(ownedAmbulanceAircrafts)
			if #ownedAmbulanceAircrafts == 0 then
				exports['ox_lib']:SendAlert('error', _U('garage_no_ambulance_aircraft'))
			else
				for _,v in pairs(ownedAmbulanceAircrafts) do
					local hashVehicule = v.vehicle.model
					local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
					local vehicleName = GetLabelText(aheadVehName)
					local plate = v.plate
					local labelvehicle
					local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
					local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

					if Config.Main.ShowVehLoc then
						if v.stored then
							labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
						else
							labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
						end
					else
						if v.stored then
							labelvehicle = labelvehicle3
						else
							labelvehicle = labelvehicle3
						end
					end

					table.insert(elements, {label = labelvehicle, value = v})
				end
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_owned_ambulance', {
				title = _U('garage_ambulance'),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				if data.current.value == nil then
				elseif data.current.value.vtype == 'aircraft' or data.current.value.vtype == 'helicopter' then
					if data.current.value.stored then
						menu.close()
						SpawnVehicle2(data.current.value.vehicle, data.current.value.plate)
					else
						exports['ox_lib']:SendAlert('inform', _U('ambulance_is_impounded'))
					end
				else
					if data.current.value.stored then
						menu.close()
						SpawnVehicle(data.current.value.vehicle, data.current.value.plate)
					else
						exports['ox_lib']:SendAlert('inform', _U('ambulance_is_impounded'))
					end
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end)
end

function StoreOwnedAmbulanceMenu()
	local playerPed  = GetPlayerPed(-1)

	if IsPedInAnyVehicle(playerPed,  false) then
		local playerPed = GetPlayerPed(-1)
		local coords = GetEntityCoords(playerPed)
		local vehicle = GetVehiclePedIsIn(playerPed, false)
		local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
		local current = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate = vehicleProps.plate

		local ValidVehicle = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, currentGarage)
		if ValidVehicle then
			StoreVehicle(vehicle, vehicleProps)
		else
			exports['ox_lib']:SendAlert('error', _U('cannot_store_vehicle'))
		end
	else
		exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
	end
end

-- End of Ambulance Code

-- Start of Police Code
function ListOwnedPoliceMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - <span style="color:red;">%s</span> |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {label = spacer, value = nil})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {label = ('<span style="color:red;">%s</span>'):format(_U('spacer1')), value = nil})
		table.insert(elements, {label = spacer, value = nil})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedPoliceCars', function(ownedPoliceCars)
		if #ownedPoliceCars == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_police'))
		else
			for _,v in pairs(ownedPoliceCars) do
				local hashVehicule = v.vehicle.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
				local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

				if Config.Main.ShowVehLoc then
					if v.stored then
						labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
					else
						labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
					end
				else
					if v.stored then
						labelvehicle = labelvehicle3
					else
						labelvehicle = labelvehicle3
					end
				end

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		table.insert(elements, {label = _U('spacer2'), value = nil})

		ESX.TriggerServerCallback('tirc-garasi:getOwnedPoliceAircrafts', function(ownedPoliceAircrafts)
			if #ownedPoliceAircrafts == 0 then
				-- exports['ox_lib']:SendAlert('error', _U('garage_no_police_aircraft'))
			else
				for _,v in pairs(ownedPoliceAircrafts) do
					local hashVehicule = v.vehicle.model
					local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
					local vehicleName = GetLabelText(aheadVehName)
					local plate = v.plate
					local labelvehicle
					local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
					local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

					if Config.Main.ShowVehLoc then
						if v.stored then
							labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
						else
							labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
						end
					else
						if v.stored then
							labelvehicle = labelvehicle3
						else
							labelvehicle = labelvehicle3
						end
					end

					table.insert(elements, {label = labelvehicle, value = v})
				end
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_owned_police', {
				title = _U('garage_police'),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				if data.current.value == nil then
				elseif data.current.value.vtype == 'aircraft' or data.current.value.vtype == 'helicopter' then
					if data.current.value.stored then
						menu.close()
						SpawnVehicle2(data.current.value.vehicle, data.current.value.plate)
					else
						exports['ox_lib']:SendAlert('inform', _U('police_is_impounded'))
					end
				else
					if data.current.value.stored then
						menu.close()
						SpawnVehicle(data.current.value.vehicle, data.current.value.plate)
					else
						exports['ox_lib']:SendAlert('inform', _U('police_is_impounded'))
					end
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end)
end

function StoreOwnedPoliceMenu()
	local playerPed  = GetPlayerPed(-1)
	if IsPedInAnyVehicle(playerPed,  false) then
		local playerPed = GetPlayerPed(-1)
		local coords = GetEntityCoords(playerPed)
		local vehicle = GetVehiclePedIsUsing(PlayerPedId())
		local vehicleProps = GetVehicleProperties(vehicle)
		local current = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate = vehicleProps.plate

		StoreVehicle(vehicle, vehicleProps)
	else
		exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
	end
end

function ReturnOwnedPoliceMenu()
	if WasinJPound then
		exports['ox_lib']:SendAlert('inform', _U('must_wait', Config.Main.JPoundWait))
	else
		ESX.TriggerServerCallback('tirc-garasi:getOutOwnedPoliceCars', function(ownedPoliceCars)
			local elements = {}

			if Config.Main.ShowVehLoc == false and Config.Main.Spacers then
				local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
				table.insert(elements, {label = spacer, value = nil})
			end

			for _,v in pairs(ownedPoliceCars) do
				local hashVehicule = v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)

				labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('return'))

				table.insert(elements, {label = labelvehicle, value = v})
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_police', {
				title = _U('pound_police', ESX.Math.GroupDigits(Config.Police.PoundP)),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				local doesVehicleExist = false

				for k,v in pairs (vehInstance) do
					if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
						if DoesEntityExist(v.vehicleentity) then
							doesVehicleExist = true
						else
							table.remove(vehInstance, k)
							doesVehicleExist = false
						end
					end
				end

				if not doesVehicleExist and not DoesAPlayerDrivesVehicle(data.current.value.plate) then
					ESX.TriggerServerCallback('tirc-garasi:checkMoneyPolice', function(hasEnoughMoney)
						if hasEnoughMoney then
							if data.current.value == nil then
							else
								SpawnVehicle(data.current.value, data.current.value.plate)
								TriggerServerEvent('tirc-garasi:payPolice')
								if Config.Main.JPoundTimer then
									WasinJPound = true
								end
							end
						else
							exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
						end
					end)
				else
					exports['ox_lib']:SendAlert('error', _U('cant_take_out'))
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end
end
-- End of Police Code

-- Start of Mechanic Code
function ListOwnedMechanicMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - <span style="color:red;">%s</span> |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {label = spacer, value = nil})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {label = ('<span style="color:red;">%s</span>'):format(_U('spacer1')), value = nil})
		table.insert(elements, {label = spacer, value = nil})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedMechanicCars', function(ownedMechanicCars)
		if #ownedMechanicCars == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_mechanic'))
		else
			for _,v in pairs(ownedMechanicCars) do
				local hashVehicule = v.vehicle.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
				local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

				if Config.Main.ShowVehLoc then
					if v.stored then
						labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
					else
						labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
					end
				else
					if v.stored then
						labelvehicle = labelvehicle3
					else
						labelvehicle = labelvehicle3
					end
				end

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_owned_mechanic', {
			title = _U('garage_mechanic'),
			align = Config.Main.MenuAlign,
			elements = elements
		}, function(data, menu)
			if data.current.value == nil then
			else
				if data.current.value.stored then
					menu.close()
					SpawnVehicle(data.current.value.vehicle, data.current.value.plate)
				else
					exports['ox_lib']:SendAlert('inform', _U('mechanic_is_impounded'))
				end
			end
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function StoreOwnedMechanicMenu()
	local playerPed  = GetPlayerPed(-1)

	if IsPedInAnyVehicle(playerPed,  false) then
		local playerPed = GetPlayerPed(-1)
		local coords = GetEntityCoords(playerPed)
		local vehicle = GetVehiclePedIsIn(playerPed, false)
		local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
		local current = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate = vehicleProps.plate

		local ValidVehicle = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, currentGarage)
		if ValidVehicle then
			if engineHealth < 990 then
				if Config.Main.DamageMult then
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Mechanic.PoundP*Config.Main.MultAmount)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				else
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Mechanic.PoundP)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				end
			else
				StoreVehicle(vehicle, vehicleProps)
			end
		end
	else
		exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
	end
end

function ReturnOwnedMechanicMenu()
	if WasinJPound then
		exports['ox_lib']:SendAlert('inform', _U('must_wait', Config.Main.JPoundWait))
	else
		ESX.TriggerServerCallback('tirc-garasi:getOutOwnedMechanicCars', function(ownedMechanicCars)
			local elements = {}

			if Config.Main.ShowVehLoc == false and Config.Main.Spacers then
				local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
				table.insert(elements, {label = spacer, value = nil})
			end

			for _,v in pairs(ownedMechanicCars) do
				local hashVehicule = v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)

				labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('return'))

				table.insert(elements, {label = labelvehicle, value = v})
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_mechanic', {
				title = _U('pound_mechanic', ESX.Math.GroupDigits(Config.Mechanic.PoundP)),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				local doesVehicleExist = false

				for k,v in pairs (vehInstance) do
					if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
						if DoesEntityExist(v.vehicleentity) then
							doesVehicleExist = true
						else
							table.remove(vehInstance, k)
							doesVehicleExist = false
						end
					end
				end

				if not doesVehicleExist and not DoesAPlayerDrivesVehicle(data.current.value.plate) then
					ESX.TriggerServerCallback('tirc-garasi:checkMoneyMechanic', function(hasEnoughMoney)
						if hasEnoughMoney then
							if data.current.value == nil then
							else
								SpawnVehicle(data.current.value, data.current.value.plate)
								TriggerServerEvent('tirc-garasi:payMechanic')
								if Config.Main.JPoundTimer then
									WasinJPound = true
								end
							end
						else
							exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
						end
					end)
				else
					exports['ox_lib']:SendAlert('error', _U('cant_take_out'))
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end
end
-- End of Mechanic Code
-- Start Of Taxi Code
function ListOwnedTaxiMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - <span style="color:red;">%s</span> |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {label = spacer, value = nil})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {label = ('<span style="color:red;">%s</span>'):format(_U('spacer1')), value = nil})
		table.insert(elements, {label = spacer, value = nil})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedTaxiCars', function(ownedMechanicCars)
		if #ownedMechanicCars == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_mechanic'))
		else
			for _,v in pairs(ownedMechanicCars) do
				local hashVehicule = v.vehicle.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
				local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

				if Config.Main.ShowVehLoc then
					if v.stored then
						labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
					else
						labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
					end
				else
					if v.stored then
						labelvehicle = labelvehicle3
					else
						labelvehicle = labelvehicle3
					end
				end

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_owned_mechanic', {
			title = _U('garage_mechanic'),
			align = Config.Main.MenuAlign,
			elements = elements
		}, function(data, menu)
			if data.current.value == nil then
			else
				if data.current.value.stored then
					menu.close()
					SpawnVehicle(data.current.value.vehicle, data.current.value.plate)
				else
					exports['ox_lib']:SendAlert('inform', _U('mechanic_is_impounded'))
				end
			end
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function StoreOwnedTaxiMenu()
	local playerPed  = GetPlayerPed(-1)

	if IsPedInAnyVehicle(playerPed,  false) then
		local playerPed = GetPlayerPed(-1)
		local coords = GetEntityCoords(playerPed)
		local vehicle = GetVehiclePedIsIn(playerPed, false)
		local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
		local current = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate = vehicleProps.plate

		local ValidVehicle = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, currentGarage)
		if ValidVehicle then
			if engineHealth < 990 then
				if Config.Main.DamageMult then
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Taxi.PoundP*Config.Main.MultAmount)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				else
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Taxi.PoundP)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				end
			else
				StoreVehicle(vehicle, vehicleProps)
			end
		end
	else
		exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
	end
end

function ReturnOwnedTaxiMenu()
	if WasinJPound then
		exports['ox_lib']:SendAlert('inform', _U('must_wait', Config.Main.JPoundWait))
	else
		ESX.TriggerServerCallback('tirc-garasi:getOutOwnedTaxiCars', function(ownedMechanicCars)
			local elements = {}

			if Config.Main.ShowVehLoc == false and Config.Main.Spacers then
				local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
				table.insert(elements, {label = spacer, value = nil})
			end

			for _,v in pairs(ownedMechanicCars) do
				local hashVehicule = v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)

				labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('return'))

				table.insert(elements, {label = labelvehicle, value = v})
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_mechanic', {
				title = _U('pound_mechanic', ESX.Math.GroupDigits(Config.Taxi.PoundP)),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				local doesVehicleExist = false

				for k,v in pairs (vehInstance) do
					if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
						if DoesEntityExist(v.vehicleentity) then
							doesVehicleExist = true
						else
							table.remove(vehInstance, k)
							doesVehicleExist = false
						end
					end
				end

				if not doesVehicleExist and not DoesAPlayerDrivesVehicle(data.current.value.plate) then
					ESX.TriggerServerCallback('tirc-garasi:checkMoneyMechanic', function(hasEnoughMoney)
						if hasEnoughMoney then
							if data.current.value == nil then
							else
								SpawnVehicle(data.current.value, data.current.value.plate)
								TriggerServerEvent('tirc-garasi:payMechanic')
								if Config.Main.JPoundTimer then
									WasinJPound = true
								end
							end
						else
							exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
						end
					end)
				else
					exports['ox_lib']:SendAlert('error', _U('cant_take_out'))
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end
end
-- End
-- Start of Aircraft Code
function ListOwnedAircraftsMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - <span style="color:red;">%s</span> |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {label = spacer, value = nil})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {label = ('<span style="color:red;">%s</span>'):format(_U('spacer1')), value = nil})
		table.insert(elements, {label = spacer, value = nil})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedAircrafts', function(ownedAircrafts)
		if #ownedAircrafts == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_aircrafts'))
		else
			for _,v in pairs(ownedAircrafts) do
				local hashVehicule = v.vehicle.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)
				local labelvehicle3 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> | '):format(plate, vehicleName)

				if Config.Main.ShowVehLoc then
					if v.stored then
						labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('loc_garage'))
					else
						labelvehicle = labelvehicle2 .. ('<span style="color:red;">%s</span> |'):format(_U('loc_pound'))
					end
				else
					if v.stored then
						labelvehicle = labelvehicle3
					else
						labelvehicle = labelvehicle3
					end
				end

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'spawn_owned_aircraft', {
			title = _U('garage_aircrafts'),
			align = Config.Main.MenuAlign,
			elements = elements
		}, function(data, menu)
			if data.current.value == nil then
			else
				if data.current.value.stored then
					menu.close()
					SpawnVehicle(data.current.value.vehicle, data.current.value.plate)
				else
					exports['ox_lib']:SendAlert('inform', _U('aircraft_is_impounded'))
				end
			end
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function StoreOwnedAircraftsMenu()
	local playerPed  = GetPlayerPed(-1)

	if IsPedInAnyVehicle(playerPed,  false) then
		local playerPed = GetPlayerPed(-1)
		local coords = GetEntityCoords(playerPed)
		local vehicle = GetVehiclePedIsIn(playerPed, false)
		local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
		local current = GetPlayersLastVehicle(GetPlayerPed(-1), true)
		local engineHealth = GetVehicleEngineHealth(current)
		local plate = vehicleProps.plate

		local ValidVehicle = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, currentGarage)
		if ValidVehicle then
			if engineHealth < 990 then
				if Config.Main.DamageMult then
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Aircrafts.PoundP*Config.Main.MultAmount)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				else
					local apprasial = math.floor((1000 - engineHealth)/1000*Config.Aircrafts.PoundP)
					RepairVehicle(apprasial, vehicle, vehicleProps)
				end
			else
				StoreVehicle(vehicle, vehicleProps)
			end
		end
	else
		exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
	end
end

function ReturnOwnedAircraftsMenu()
	if WasInPound then
		exports['ox_lib']:SendAlert('inform', _U('must_wait', Config.Main.PoundWait))
	else
		ESX.TriggerServerCallback('tirc-garasi:getOutOwnedAircrafts', function(ownedAircrafts)
			local elements = {}

			if Config.Main.ShowVehLoc == false and Config.Main.Spacers then
				local spacer = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> |'):format(_U('plate'), _U('vehicle'))
				table.insert(elements, {label = spacer, value = nil})
			end

			for _,v in pairs(ownedAircrafts) do
				local hashVehicule = v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName = GetLabelText(aheadVehName)
				local plate = v.plate
				local labelvehicle
				local labelvehicle2 = ('| <span style="color:red;">%s</span> - <span style="color:darkgoldenrod;">%s</span> - '):format(plate, vehicleName)

				labelvehicle = labelvehicle2 .. ('<span style="color:green;">%s</span> |'):format(_U('return'))

				table.insert(elements, {label = labelvehicle, value = v})
			end

			ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_aircraft', {
				title = _U('pound_aircrafts', ESX.Math.GroupDigits(Config.Aircrafts.PoundP)),
				align = Config.Main.MenuAlign,
				elements = elements
			}, function(data, menu)
				local doesVehicleExist = false

				for k,v in pairs (vehInstance) do
					if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
						if DoesEntityExist(v.vehicleentity) then
							doesVehicleExist = true
						else
							table.remove(vehInstance, k)
							doesVehicleExist = false
						end
					end
				end

				if not doesVehicleExist and not DoesAPlayerDrivesVehicle(data.current.value.plate) then
					ESX.TriggerServerCallback('tirc-garasi:checkMoneyAircrafts', function(hasEnoughMoney)
						if hasEnoughMoney then
							if data.current.value == nil then
							else
								SpawnVehicle(data.current.value, data.current.value.plate)
								TriggerServerEvent('tirc-garasi:payAircraft')
								if Config.Main.PoundTimer then
									WasInPound = true
								end
							end
						else
							exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
						end
					end)
				else
					exports['ox_lib']:SendAlert('error', _U('cant_take_out'))
				end
			end, function(data, menu)
				menu.close()
			end)
		end)
	end
end
-- End of Aircraft Code

-- Start of Boat Code
function ListOwnedBoatsMenu()
	local elements = {}

	if Config.Main.ShowVehLoc and Config.Main.Spacers then
		local spacer = ('| %s - %s - %s |'):format(_U('plate'), _U('vehicle'), _U('location'))
		table.insert(elements, {
			title = spacer,
			description = nil,
			disabled = true,
		})
	elseif Config.Main.ShowVehLoc == false and Config.Main.Spacers then
		local spacer = ('| %s - %s |'):format(_U('plate'), _U('vehicle'))
		table.insert(elements, {
			title = _U('spacer1'),
			description = nil,
			disabled = true,
		})
		table.insert(elements, {
			title = spacer,
			description = nil,
			disabled = true,
		})
	end

	ESX.TriggerServerCallback('tirc-garasi:getOwnedBoats', function(ownedBoats)
		if #ownedBoats == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_boats'))
			return
		end

		for _, v in pairs(ownedBoats) do
			local hashVehicule = v.vehicle.model
			local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
			local vehicleName = GetLabelText(aheadVehName)
			local plate = v.plate
			local label

			if Config.Main.ShowVehLoc then
				if v.stored then
					label = ('%s - %s - %s'):format(plate, vehicleName, _U('loc_garage'))
				else
					label = ('%s - %s - %s'):format(plate, vehicleName, _U('loc_pound'))
				end
			else
				label = ('%s - %s'):format(plate, vehicleName)
			end

			table.insert(elements, {
				title = label,
				description = nil,
				metadata = {plate = plate},
				onSelect = function()
					if v.stored then
						SpawnVehicle(v.vehicle, plate)
					else
						exports['ox_lib']:SendAlert('inform', _U('boat_is_impounded'))
					end
				end
			})
		end

		lib.registerContext({
			id = 'owned_boats_menu',
			title = _U('garage_boats'),
			options = elements
		})

		lib.showContext('owned_boats_menu')
	end)
end

function StoreOwnedBoatsMenu()
    local playerPed = GetPlayerPed(-1)

    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)
        local engineHealth = GetVehicleEngineHealth(vehicle)
        local plate = vehicleProps.plate

		local ValidVehicle = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, currentGarage)
		if ValidVehicle then
			if engineHealth < 990 then
				local apprasial
				if Config.Main.DamageMult then
					apprasial = math.floor((1000 - engineHealth) / 1000 * Config.Boats.PoundP * Config.Main.MultAmount)
				else
					apprasial = math.floor((1000 - engineHealth) / 1000 * Config.Boats.PoundP)
				end
				RepairVehicle(apprasial, vehicle, vehicleProps)
			else
				StoreVehicle(vehicle, vehicleProps)
			end
		end
    else
        exports['ox_lib']:SendAlert('error', _U('no_vehicle_to_enter'))
    end
end


function ReturnOwnedBoatsMenu()
	if WasInPound then
		exports['ox_lib']:SendAlert('inform', _U('must_wait', Config.Main.PoundWait))
		return
	end

	ESX.TriggerServerCallback('tirc-garasi:getOutOwnedBoats', function(ownedBoats)
		if #ownedBoats == 0 then
			exports['ox_lib']:SendAlert('error', _U('garage_no_boats'))
			return
		end

		local options = {}

		if Config.Main.ShowVehLoc == false and Config.Main.Spacers then
			local spacer = ('| %s - %s |'):format(_U('plate'), _U('vehicle'))
			table.insert(options, {
				title = spacer,
				disabled = true
			})
		end

		for _, v in pairs(ownedBoats) do
			local hashVehicule = v.model
			local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
			local vehicleName = GetLabelText(aheadVehName)
			local plate = v.plate
			local title = ('%s - %s - %s'):format(plate, vehicleName, _U('return'))

			table.insert(options, {
				title = title,
				metadata = { plate = plate },
				onSelect = function()
					local doesVehicleExist = false

					for k, veh in pairs(vehInstance) do
						if ESX.Math.Trim(veh.plate) == ESX.Math.Trim(v.plate) then
							if DoesEntityExist(veh.vehicleentity) then
								doesVehicleExist = true
							else
								table.remove(vehInstance, k)
							end
						end
					end

					if not doesVehicleExist and not DoesAPlayerDrivesVehicle(v.plate) then
						ESX.TriggerServerCallback('tirc-garasi:checkMoneyBoats', function(hasEnoughMoney)
							if hasEnoughMoney then
								SpawnVehicle(v, v.plate)
								TriggerServerEvent('tirc-garasi:payBoat')
								if Config.Main.PoundTimer then
									WasInPound = true
								end
							else
								exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
							end
						end)
					else
						exports['ox_lib']:SendAlert('error', _U('cant_take_out'))
					end
				end
			})
		end

		lib.registerContext({
			id = 'return_owned_boat_menu',
			title = _U('pound_boats', ESX.Math.GroupDigits(Config.Boats.PoundP)),
			options = options
		})

		lib.showContext('return_owned_boat_menu')
	end)
end
-- End of Boat Code

-- Start of Car Code
local function ListOwnedCarsMenu(Garage)
    local elements = {}
    local vehicleNames = {
        [-808831384] = 'Baller',
        [-1917086021] = 'Mobil Pasukan Elite',
        [1067067984] = 'AMG G63 6x6 Polisi',
        [2071877360] = 'Barracuda Brimob',
        [-227663167] = 'Riot Polisi',
        [630371791] = 'Truk Tahanan',
        [2090754350] = 'Mazda 6 Polisi',
        [-56385083] = 'Motor Trail Polisi',
        [-1369204559] = 'Corvette Polisi',
        [739837319] = 'Dodge Polisi',
        [-313039536] = 'Hammer Polisi',
        [1564144270] = 'R 1200 RTP',
        [1416125959] = 'R 1200 GS',
        [-1682547979] = 'Ranger Polisi',
        [-828048483] = 'Motor Trail Medis',
        [1221510024] = 'Nissan Titan Warrior'
    }

    ESX.TriggerServerCallback('tirc-garasi:getOwnedCars', function(ownedCars)
        if not Garage then return end

        if #ownedCars == 0 then
            exports['ox_lib']:SendAlert('error', _U('garage_no_cars'))
            return
        end

        for _, v in pairs(ownedCars) do
            local hashVehicule = v.vehicle.model
            local vehicleName = v.vehiclename or vehicleNames[hashVehicule] or GetLabelText(GetDisplayNameFromVehicleModel(hashVehicule))
            local plate = v.plate
            local status, location, allowed

            if v.stored then
                status = 'Stored'
                location = 'In Garage'
                allowed = true
            else
                status = 'Exiting'
                location = 'Public Impound'
                allowed = false
            end

            table.insert(elements, {
                status = status,
                vehicleName = vehicleName,
                state = v.stored,
                plate = plate,
                VehProp = json.encode(v.vehicle),
                location = location,
                allowed = allowed
            })
        end

        SendNUIMessage({
            action = "openVehiclesList",
            data = elements,
        })

        SetNuiFocus(true, true)
    end, Garage)
end


RegisterNUICallback("ExitBtn" , function(data , cb)
    SendNUIMessage({
        action = "CloseVehiclesList"
    })
    -- SetTimecycleModifier('default')
	-- Test
    SetNuiFocus(false , false)
    cb("ok")
end)

RegisterNUICallback("takeout", function(data, cb)
    SendNUIMessage({
		action = "CloseVehiclesList"
	})
    SetNuiFocus(false, false)
    Citizen.Wait(500)
    local vehprop = data.selected

    if vehprop == 'exit' then
        exports['ox_lib']:Notify({ type = 'error', description = 'Your vehicle is outside. Take it from the public impound.' })
        cb("ok")
        return
    end

    local result = lib.callback.await('rnr_garage:Server:validateAndTakeVehicle', false, vehprop.plate)
    if not result then
        exports['ox_lib']:Notify({ type = 'error', description = 'Kendaraan tidak ditemukan atau tidak valid.' })
        cb("ok")
        return
    end
    TriggerEvent('spawnvehicle', result.vehicle)
    cb("ok")
end)

local spawnLocked = false

function IsSpawnPointClear(coords, radius)
    local vehicles = GetGamePool("CVehicle")
    for _, veh in pairs(vehicles) do
        if DoesEntityExist(veh) then
            local vehCoords = GetEntityCoords(veh)
            if #(vehCoords - coords) < radius then
                return false
            end
        end
    end
    return true
end


function GetValidSpawnPoint(garage)
    for _, point in ipairs(garage.SpawnPoints) do
        local coords = vector3(point.x, point.y, point.z)
        if IsSpawnPointClear(coords, 3.5) then
            return point
        end
    end
    return nil
end

RegisterNetEvent('spawnvehicle')
AddEventHandler('spawnvehicle', function(v)
    if spawnLocked then
        TriggerEvent('ox_lib:notify', {
            type = 'error',
            description = 'Tunggu sebentar sebelum spawn lagi.'
        })
        return
    end

    spawnLocked = true
    SetTimeout(3000, function()
        spawnLocked = false
    end)

    local garage = Garasi_L
    local point = GetValidSpawnPoint(garage)

    if not point then
        TriggerEvent('ox_lib:notify', {
            type = 'error',
            description = 'Semua titik spawn di garasi penuh.'
        })
        return
    end

    ESX.Game.SpawnVehicle(v.model, {
        x = point.x,
        y = point.y,
        z = point.z + 1.0
    }, point.h, function(callback_vehicle)
        SetVehicleProperties(callback_vehicle, v)
        SetModelAsNoLongerNeeded(v.model)
        SetVehicleUndriveable(callback_vehicle, false)
        SetVehicleEngineOn(callback_vehicle, true, true)
        SetEntityAsMissionEntity(callback_vehicle, true, false)
        SetVehicleHasBeenOwnedByPlayer(callback_vehicle, true)
        SetPedIntoVehicle(PlayerPedId(), callback_vehicle, -1)

        local carplate = GetVehicleNumberPlateText(callback_vehicle)
        table.insert(vehInstance, {
            vehicleentity = callback_vehicle,
            pek = carplate
        })

        local fuelLevel = v.fuelLevel or 100.0
        Entity(callback_vehicle).state.fuel = fuelLevel
    end, true)

    TriggerServerEvent('tirc-garasi:setVehicleState', v.plate, false)
end)


RegisterNetEvent('poundmenu')
AddEventHandler('poundmenu', function(v)
    ESX.Game.SpawnVehicle(v.model, {
        x = this_Garage.SpawnPoint2.x,
        y = this_Garage.SpawnPoint2.y,
        z = this_Garage.SpawnPoint2.z + 1
    }, this_Garage.SpawnPoint2.h, function(callback_vehicle)
        SetVehicleProperties(callback_vehicle, v)
        SetModelAsNoLongerNeeded(v.model)
        SetVehicleUndriveable(callback_vehicle, false)
        SetVehicleEngineOn(callback_vehicle, true, true)
        SetEntityAsMissionEntity(callback_vehicle, true, false)
        SetVehicleHasBeenOwnedByPlayer(callback_vehicle, true)

        local carplate = GetVehicleNumberPlateText(callback_vehicle)
        table.insert(vehInstance, {vehicleentity = callback_vehicle, pek = carplate})

        -- Set fuel using ox_fuel
        local fuelLevel = v.fuelLevel or 100.0
        Entity(callback_vehicle).state.fuel = fuelLevel

        TaskWarpPedIntoVehicle(GetPlayerPed(-1), callback_vehicle, -1)
    end, true)
	TriggerServerEvent('tirc-garasi:setVehicleState', v.plate, false)
end)



function StoreOwnedCarsMenu(Garage)
    local playerPed = PlayerPedId()

    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsUsing(playerPed)
        local vehicleProps = GetVehicleProperties(vehicle)
        local plate = vehicleProps.plate
        local fuelLevel = Entity(vehicle).state.fuel or 50.0

        local engineHealth = math.ceil(GetVehicleEngineHealth(vehicle))
        local bodyHealth = math.ceil(GetVehicleBodyHealth(vehicle))

        vehicleProps.engineHealth = engineHealth
        vehicleProps.bodyHealth = bodyHealth
        vehicleProps.fuelLevel = fuelLevel

        local result = lib.callback.await('tirc-garasi:storeVehicle', false, vehicleProps, Garage)

        if result then
            StoreVehicle(vehicle, vehicleProps)
        else
            exports['ox_lib']:notify({
                type = 'error',
                description = 'Gagal menyimpan kendaraan!',
                duration = 5000
            })
        end
    else
        exports['ox_lib']:notify({
            type = 'error',
            description = _U('no_vehicle_to_enter'),
            duration = 5000
        })
    end
end



function ReturnOwnedAllMenu()
	ESX.TriggerServerCallback('tirc-garasi:getOutOwnedCars', function(ownedCars)
		local elements = {}

		if Config.ShowPoundSpacer2 then
			table.insert(elements, {label = _U('spacer2')})
		end

		if Config.ShowPoundSpacer3 then
			table.insert(elements, {label = _U('spacer3')})
		end

		for _,v in pairs(ownedCars) do

			local hashVehicule =  v.vehicle.model
			local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
			local plate        = v.plate
			local vehicleName
			if v.vehiclename then
				vehicleName = v.vehiclename
			else
				vehicleName = GetLabelText(aheadVehName)
			end


			if v.stored then
				status = 'Stored'
				location = 'Public Impound'
				allowed = true
			else
				status = 'Exiting'
				location = 'Public Impound'
				allowed = false
			end

			elements = {}
			table.insert(elements, {
				status = status,
				vehicleName = vehicleName,
				state = v.stored,
				plate = plate,
				VehProp = json.encode(v.vehicle),
				location = location,
				allowed = allowed,
				price = price
			})
            SendNUIMessage({
                action = "openVehiclesListImpound",
                data = elements,
            })
            -- SetTimecycleModifier('hud_def_blur')
            SetNuiFocus(true , true)

		end
	end)
end

RegisterNUICallback("takeout2", function(data, cb)
    SendNUIMessage({ action = "CloseVehiclesList" })
    SetNuiFocus(false, false)
    Wait(500)

    local vehprop = data.selected
    local plate = vehprop.plate

    if not plate then
        exports['ox_lib']:SendAlert('error', 'Invalid vehicle plate')
        cb("ok")
        return
    end

    if takenOutVehicles[plate] then
        exports['ox_lib']:SendAlert('error', 'Vehicle already taken out!')
        cb("ok")
        return
    end

    if vehprop == 'impounded' then
        exports['ox_lib']:SendAlert('error', 'Your vehicle is impounded')
    else
        ESX.TriggerServerCallback("checkMoney", function(hasEnoughMoney)
            if hasEnoughMoney then
                takenOutVehicles[plate] = true
                TriggerEvent("poundmenu", vehprop)
				SetTimeout(600000, function()
					takenOutVehicles[plate] = false
				end)
            else
                exports['ox_lib']:SendAlert('error', 'Not enough money')
            end
        end)
    end
    cb("ok")
end)



-- WasInPound & WasinJPound Code
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)

		if Config.Main.PoundTimer then
			if WasInPound then
				Citizen.Wait(Config.Main.PoundWait * 60000)
				WasInPound = false
			end
		end

		if Config.Main.JPoundTimer then
			if WasinJPound then
				Citizen.Wait(Config.Main.JPoundWait * 60000)
				WasinJPound = false
			end
		end
	end
end)

-- Repair Vehicles
function RepairVehicle(apprasial, vehicle, vehicleProps)
	ESX.UI.Menu.CloseAll()

	local elements = {
		{label = _U('return_vehicle').." ($"..apprasial..")", value = 'yes'},
		{label = _U('see_mechanic'), value = 'no'}
	}

	ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'delete_menu', {
		title = _U('damaged_vehicle'),
		align = Config.Main.MenuAlign,
		elements = elements
	}, function(data, menu)
		menu.close()

		if data.current.value == 'yes' then
			TriggerServerEvent('tirc-garasi:payhealth', apprasial)
			vehicleProps.bodyHealth = 1000.0 -- must be a decimal value!!!
			vehicleProps.engineHealth = 1000
			StoreVehicle(vehicle, vehicleProps)
		elseif data.current.value == 'no' then
			exports['ox_lib']:SendAlert('inform', _U('visit_mechanic'))
		end
	end, function(data, menu)
		menu.close()
	end)
end

-- Store Vehicles
function StoreVehicle(vehicle, vehicleProps)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Cek apakah ada di BoatGarages
    for _, v in pairs(Config.BoatGarages) do
        if #(coords - vec3(v.Deleter.x, v.Deleter.y, v.Deleter.z)) < 15 then
            TaskLeaveVehicle(playerPed, vehicle, 1)
            Wait(500)
            DeleteEntity(vehicle)
            TriggerServerEvent('tirc-garasi:setVehicleState', vehicleProps.plate, true)
            SetEntityCoords(playerPed, v.Marker.x, v.Marker.y, v.Marker.z)
            Wait(300)
            return
        end
    end
    TaskLeaveVehicle(playerPed, vehicle, 1)
    Wait(1500)
    DeleteEntity(vehicle)
    TriggerServerEvent('tirc-garasi:setVehicleState', vehicleProps.plate, true)
end


SetVehicleProperties = function(vehicle, vehicleProps)
    if not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then return end

    ESX.Game.SetVehicleProperties(vehicle, vehicleProps)

    -- Set Engine, Body, and Fuel
    SetVehicleEngineHealth(vehicle, vehicleProps["engineHealth"] and vehicleProps["engineHealth"] + 0.0 or 1000.0)
    SetVehicleBodyHealth(vehicle, vehicleProps["bodyHealth"] and vehicleProps["bodyHealth"] + 0.0 or 1000.0)
    SetVehicleFuelLevel(vehicle, vehicleProps["fuelLevel"] and vehicleProps["fuelLevel"] + 0.0 or 100.0)

    -- Set Windows
    if vehicleProps["windows"] then
        for windowId = 1, #vehicleProps["windows"] do
            if vehicleProps["windows"][windowId] == false then
                SmashVehicleWindow(vehicle, windowId - 1)
            end
        end
    end

    -- Set Tyres
    if vehicleProps["tyres"] then
        for tyreId = 0, #vehicleProps["tyres"] - 1 do
            if vehicleProps["tyres"][tyreId + 1] then
                SetVehicleTyreBurst(vehicle, tyreId, true, 1000)
            end
        end
    end

    -- Set Doors
    if vehicleProps["doors"] then
        for doorId = 0, #vehicleProps["doors"] - 1 do
            if vehicleProps["doors"][doorId + 1] then
                SetVehicleDoorBroken(vehicle, doorId, true)
            end
        end
    end
end

GetVehicleProperties = function(vehicle)
    if not DoesEntityExist(vehicle) then return end

    local vehicleProps = ESX.Game.GetVehicleProperties(vehicle)

    vehicleProps["tyres"] = {}
    vehicleProps["windows"] = {}
    vehicleProps["doors"] = {}

    for id = 0, 7 do
        local tyreBurst = IsVehicleTyreBurst(vehicle, id, false)
        if not tyreBurst then
            tyreBurst = IsVehicleTyreBurst(vehicle, id, true)
        end
        table.insert(vehicleProps["tyres"], tyreBurst or false)
    end

    for id = 0, 7 do
        local intact = IsVehicleWindowIntact(vehicle, id)
        if intact == nil then intact = true end
        table.insert(vehicleProps["windows"], intact)
    end

    for id = 0, 5 do
        local damaged = IsVehicleDoorDamaged(vehicle, id)
        table.insert(vehicleProps["doors"], damaged or false)
    end

    vehicleProps["engineHealth"] = GetVehicleEngineHealth(vehicle)
    vehicleProps["bodyHealth"] = GetVehicleBodyHealth(vehicle)
    vehicleProps["fuelLevel"] = GetVehicleFuelLevel(vehicle)

    return vehicleProps
end

-- Spawn Vehicles
function SpawnVehicle(vehicle, plate)
	ESX.Game.SpawnVehicle(vehicle.model, this_Garage.Spawner, this_Garage.Heading, function(callback_vehicle)
		SetVehicleProperties(callback_vehicle, vehicle)
		SetModelAsNoLongerNeeded(vehicle.model)
		SetVehicleUndriveable(callback_vehicle, false)
		SetVehicleEngineOn(callback_vehicle, true, true)
		SetEntityAsMissionEntity(callback_vehicle, true, false)
		SetVehicleHasBeenOwnedByPlayer(callback_vehicle, true)

        local carplate = GetVehicleNumberPlateText(plate)
		table.insert(vehInstance, {vehicleentity = callback_vehicle, plate = carplate})
		Entity(callback_vehicle).state.fuel = GetVehicleFuelLevel(callback_vehicle)
		TaskWarpPedIntoVehicle(GetPlayerPed(-1), callback_vehicle, -1)
    end)

    TriggerServerEvent('tirc-garasi:setVehicleState', plate, false)
end

function SpawnImpoundCar(vehicle, plate)
	ESX.Game.SpawnVehicle(vehicle.model, {
		x = this_Garage.SpawnPoint2.x,
		y = this_Garage.SpawnPoint2.y,
		z = this_Garage.SpawnPoint2.z + 1
	}, this_Garage.SpawnPoint2.h, this_Garage.SpawnPoint2.h, function(callback_vehicle)
		SetVehicleProperties(callback_vehicle, vehicle)
		SetModelAsNoLongerNeeded(vehicle.model)
		SetVehicleUndriveable(callback_vehicle, false)
		SetVehicleEngineOn(callback_vehicle, true, true)
		SetEntityAsMissionEntity(callback_vehicle, true, false)
		SetVehicleHasBeenOwnedByPlayer(callback_vehicle, true)

        local carplate = GetVehicleNumberPlateText(plate)
		table.insert(vehInstance, {vehicleentity = callback_vehicle, plate = carplate})
		exports["zero-bensin"]:SetFuel(callback_vehicle, 100)
		TaskWarpPedIntoVehicle(GetPlayerPed(-1), callback_vehicle, -1)
    end, true)

    TriggerServerEvent('tirc-garasi:setVehicleState', plate, false)
end

-- Check Vehicles
function DoesAPlayerDrivesVehicle(plate)
	local isVehicleTaken = false
	local players = ESX.Game.GetPlayers()
	for i=1, #players, 1 do
		local target = GetPlayerPed(players[i])
		if target ~= PlayerPedId() then
			local plate1 = GetVehicleNumberPlateText(GetVehiclePedIsIn(target, true))
			local plate2 = GetVehicleNumberPlateText(GetVehiclePedIsIn(target, false))
			if plate == plate1 or plate == plate2 then
				isVehicleTaken = true
				break
			end
		end
	end
	return isVehicleTaken
end

-- Entered Marker
AddEventHandler('tirc-garasi:hasEnteredMarker', function(zone)
	if zone == 'ambulance_garage_point' then
		CurrentAction = 'ambulance_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'ambulance_store_point' then
		CurrentAction = 'ambulance_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'ambulance_pound_point' then
		CurrentAction = 'ambulance_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	elseif zone == 'police_garage_point' then
		CurrentAction = 'police_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'police_store_point' then
		CurrentAction = 'police_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'police_pound_point' then
		CurrentAction = 'police_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	elseif zone == 'mechanic_garage_point' then
		CurrentAction = 'mechanic_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'mechanic_store_point' then
		CurrentAction = 'mechanic_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'mechanic_pound_point' then
		CurrentAction = 'mechanic_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	elseif zone == 'aircraft_garage_point' then
		CurrentAction = 'aircraft_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'aircraft_store_point' then
		CurrentAction = 'aircraft_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'aircraft_pound_point' then
		CurrentAction = 'aircraft_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	elseif zone == 'boat_garage_point' then
		CurrentAction = 'boat_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'boat_store_point' then
		CurrentAction = 'boat_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'boat_pound_point' then
		CurrentAction = 'boat_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	elseif zone == 'car_garage_point' then
		CurrentAction = 'car_garage_point'
		CurrentActionMsg = _U('press_to_enter')
		CurrentActionData = {}
	elseif zone == 'car_store_point' then
		CurrentAction = 'car_store_point'
		CurrentActionMsg = _U('press_to_delete')
		CurrentActionData = {}
	elseif zone == 'car_pound_point' then
		CurrentAction = 'car_pound_point'
		CurrentActionMsg = _U('press_to_impound')
		CurrentActionData = {}
	end
end)

-- Exited Marker
AddEventHandler('tirc-garasi:hasExitedMarker', function()
	ESX.UI.Menu.CloseAll()
	CurrentAction = nil
end)

-- Resource Stop
AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
		ESX.UI.Menu.CloseAll()
	end
end)

RegisterNetEvent('garageVeh')
AddEventHandler('garageVeh', function()
    this_Garage = nil
    currentZone = ''
    currentGarage = nil

    local playerPed = PlayerPedId()
    local coords    = GetEntityCoords(playerPed)

    for k,v in pairs(Config.CarGarages) do
        if IsPedInAnyVehicle(playerPed, false) then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if GetPedInVehicleSeat(vehicle, -1) == playerPed then
                if (GetDistanceBetweenCoords(coords, v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z, true) < 15) then
                    this_Garage = v
                    currentZone = 'car_store_point'
                    currentGarage= v.Name
                end
            end
        else
            if (GetDistanceBetweenCoords(coords, v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z, true) < 15) then
                this_Garage = v
                currentZone = 'car_garage_point'
                currentGarage= v.Name
            end
        end
    end

    for _,v in pairs(Config.PrivateCarGarages) do
        if IsPedInAnyVehicle(playerPed, false) then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if GetPedInVehicleSeat(vehicle, -1) == playerPed then
                if(GetDistanceBetweenCoords(coords, v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z, true) < 15) then
                    this_Garage = v
                    currentZone = 'car_store_point'
                    currentGarage= v.Name
                end
            end
        else
            if (GetDistanceBetweenCoords(coords, v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z, true) < 15) then
                this_Garage = v
                currentZone = 'car_garage_point'
                currentGarage= v.Name
            end
        end
    end

    for _,v in pairs(Config.CarPounds) do
        if (GetDistanceBetweenCoords(coords, v.PoundPoint.x, v.PoundPoint.y, v.PoundPoint.z, true) < 15) then
            this_Garage = v
            currentZone = 'car_pound_point'
            currentGarage= v.Name
        end
    end

    -- Boat Garages
    for _, v in pairs(Config.BoatGarages) do
        if IsPedInAnyVehicle(playerPed, false) then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if GetPedInVehicleSeat(vehicle, -1) == playerPed then
                if #(coords - vec3(v.Deleter.x, v.Deleter.y, v.Deleter.z)) < 15 then
                    this_Garage = v
                    currentZone = 'boat_store_point'
                    currentGarage = v.Name
                end
            end
        else
            if #(coords - vec3(v.Marker.x, v.Marker.y, v.Marker.z)) < 15 then
                this_Garage = v
                currentZone = 'boat_garage_point'
                currentGarage = v.Name
            end
        end
    end

    for _,v in pairs(Config.BoatPounds) do
        if (GetDistanceBetweenCoords(coords, v.Marker.x, v.Marker.y, v.Marker.z, true) < 15) then
            this_Garage = v
            currentZone = 'boat_pound_point'
            currentGarage= v.Name
        end
    end

    if currentZone == '' or currentGarage == nil then
        AdaDiGarasi = false
    end

    OpenGarageMenu(currentZone, currentGarage)
end)

RegisterNetEvent('garage:accessResult')
AddEventHandler('garage:accessResult', function(allowed, garageLabel)
    if allowed then
        lib.showTextUI('[F1] - ' .. garageLabel)
    else
        lib.showTextUI('🚫 Akses ditolak')
        SetTimeout(3000, lib.hideTextUI)
    end
end)


function AdaDiGarasi()
    return AdaDiGarasi
end

exports('AdaDiGarasi', AdaDiGarasi)

for k, v in pairs(Config.CarGarages) do
    lib.zones.box({
        name = "Garage"..v.Name,
        coords = vec3(v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z),
        size = vec3(10.5, 10.5, 3.0),
        rotation = 169.5,
        debug = true,
        onEnter = function()
            AdaDiGarasi = false
            lib.showTextUI('[F1] - Garasi')
        end,
        onExit = function()
            AdaDiGarasi = true
            lib.hideTextUI()
        end
    })
end

for k, v in pairs(Config.PrivateCarGarages) do
	lib.zones.box({
		name = "Garage"..v.Name,
		coords = vec3(v.GaragePoint.x, v.GaragePoint.y, v.GaragePoint.z),
		size = vec3(10.5, 10.5, 3.0),
		rotation = 169.5,
		debug = false,
		onEnter = function()
			AdaDiGarasi = false
			lib.showTextUI('[F1] - Garasi')
		end,
		onExit = function()
			AdaDiGarasi = true
			lib.hideTextUI()
		end
	})
end

for k, v in pairs(Config.CarPounds) do
	lib.zones.box({
		name = "Garage"..v.Name,
		coords = vec3(v.PoundPoint.x, v.PoundPoint.y, v.PoundPoint.z),
		size = vec3(10.5, 10.5, 3.0),
		rotation = 169.5,
		debug = false,
		onEnter = function()
			AdaDiGarasi = false
			lib.showTextUI('[F1] - Asuransi')
		end,
		onExit = function()
			AdaDiGarasi = true
			lib.hideTextUI()
		end
	})
end

for k, v in pairs(Config.PolicePounds) do
	lib.zones.box({
		name = "Pound"..v.Name,
		coords = vec3(v.PoundPoint.x, v.PoundPoint.y, v.PoundPoint.z),
		size = vec3(10.5, 10.5, 3.0),
		rotation = 169.5,
		debug = false,
		onEnter = function()
			AdaDiGarasi = false
			lib.showTextUI('[F1] - Garage Boat')
		end,
		onExit = function()
			AdaDiGarasi = true
			lib.hideTextUI()
		end
	})
end

for k, v in pairs(Config.BoatGarages) do
	lib.zones.box({
		name = "Garage"..v.Name,
		coords = vec3(v.Marker.x, v.Marker.y, v.Marker.z),
		size = vec3(10.5, 10.5, 25.0),
		rotation = 169.5,
		debug = false,
		onEnter = function()
			AdaDiGarasi = false
			lib.showTextUI('[F1] - Garage Boat')
		end,
		onExit = function()
			AdaDiGarasi = true
			lib.hideTextUI()
		end
	})
end

for k, v in pairs(Config.BoatPounds) do
	lib.zones.box({
		name = "Pound"..v.Name,
		coords = vec3(v.Marker.x, v.Marker.y, v.Marker.z),
		size = vec3(10.5, 10.5, 3.0),
		rotation = 169.5,
		debug = false,
		onEnter = function()
			AdaDiGarasi = false
			lib.showTextUI('[F1] - Impound Boat')
		end,
		onExit = function()
			AdaDiGarasi = true
			lib.hideTextUI()
		end
	})
end

function ReturnOwnedPoliceMenu()
	ESX.TriggerServerCallback('tirc-garasi:getOutOwnedPoliceCars', function(ownedPoliceCars)
		local elements = {}

		if Config.ShowPoundSpacer2 then
			table.insert(elements, {label = _U('spacer2')})
		end

		if Config.ShowPoundSpacer3 then
			table.insert(elements, {label = _U('spacer3')})
		end

		for _,v in pairs(ownedPoliceCars) do
			if Config.UseVehicleNamesLua then
				local hashVehicule =  v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName  = GetLabelText(aheadVehName)
				local plate        = v.plate
				local labelvehicle

				local vehiclePrice = 500

				if v.job == nil then
					for i=1, #Vehicles, 1 do
						if hashVehicule == GetHashKey(Vehicles[i].model) then
							vehiclePrice = math.floor((Vehicles[i].price * persenPrice)+0.5)
							break
						end
					end
				end

				labelvehicle = plate..' - '..vehicleName..' - <span style="color:green;">$'..ESX.Math.GroupDigits(vehiclePrice) .. '</span>'

				table.insert(elements, {label = labelvehicle, value = v})
			else
				local hashVehicule =  v.model
				local vehicleName  = GetDisplayNameFromVehicleModel(hashVehicule)
				local plate        = v.plate
				local labelvehicle

				labelvehicle = plate..' - '..vehicleName

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_car', {
			title    = ('Impound Menu'),
			align    = 'bottom-right',
			elements = elements
		}, function(data, menu)
			for k,v in pairs (vehInstance) do
				if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
					if DoesEntityExist(v.vehicleentity) then
						doesVehicleExist = true
					else
						table.remove(vehInstance, k)
						doesVehicleExist = false
					end
				end
			end

			ESX.TriggerServerCallback('tirc-garasi:checkMoneyCars', function(hasEnoughMoney)
				if hasEnoughMoney then
					menu.close()
					TriggerServerEvent('tirc-garasi:payCar')
					TriggerEvent('poundmenu', data.current.value)
				else
					exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
					menu.close()
				end
			end, data.current.value.model, data.current.value.job)
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function ReturnOwnedAmbulanceMenu()
	ESX.TriggerServerCallback('tirc-garasi:getOutOwnedAmbulanceCars', function(ownedAmbulanceCars)
		local elements = {}

		if Config.ShowPoundSpacer2 then
			table.insert(elements, {label = _U('spacer2')})
		end

		if Config.ShowPoundSpacer3 then
			table.insert(elements, {label = _U('spacer3')})
		end

		for _,v in pairs(ownedAmbulanceCars) do
			if Config.UseVehicleNamesLua then
				local hashVehicule =  v.model
				local aheadVehName = GetDisplayNameFromVehicleModel(hashVehicule)
				local vehicleName  = GetLabelText(aheadVehName)
				local plate        = v.plate
				local labelvehicle

				local vehiclePrice = 500

				if v.job == nil then
					for i=1, #Vehicles, 1 do
						if hashVehicule == GetHashKey(Vehicles[i].model) then
							vehiclePrice = math.floor((Vehicles[i].price * persenPrice)+0.5)
							break
						end
					end
				end

				labelvehicle = plate..' - '..vehicleName..' - <span style="color:green;">$'..ESX.Math.GroupDigits(vehiclePrice) .. '</span>'

				table.insert(elements, {label = labelvehicle, value = v})
			else
				local hashVehicule =  v.model
				local vehicleName  = GetDisplayNameFromVehicleModel(hashVehicule)
				local plate        = v.plate
				local labelvehicle

				labelvehicle = plate..' - '..vehicleName

				table.insert(elements, {label = labelvehicle, value = v})
			end
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'return_owned_car', {
			title    = ('Impound Menu'),
			align    = 'bottom-right',
			elements = elements
		}, function(data, menu)
			for k,v in pairs (vehInstance) do
				if ESX.Math.Trim(v.plate) == ESX.Math.Trim(data.current.value.plate) then
					if DoesEntityExist(v.vehicleentity) then
						doesVehicleExist = true
					else
						table.remove(vehInstance, k)
						doesVehicleExist = false
					end
				end
			end

			ESX.TriggerServerCallback('tirc-garasi:checkMoneyCars', function(hasEnoughMoney)
				if hasEnoughMoney then
					menu.close()
					TriggerServerEvent('tirc-garasi:payCar')
					TriggerEvent('poundmenu', data.current.value)
				else
					exports['ox_lib']:SendAlert('error', _U('not_enough_money'))
					menu.close()
				end
			end, data.current.value.model, data.current.value.job)
		end, function(data, menu)
			menu.close()
		end)
	end)
end

function OpenGarageMenu(PointType, Garage)
	if PointType == 'car_garage_point' then
		ListOwnedCarsMenu(Garage)
	elseif PointType == 'car_store_point' then
		StoreOwnedCarsMenu(Garage)
	elseif PointType == 'car_pound_point' then
		ReturnOwnedAllMenu()
	elseif PointType == 'boat_garage_point' then
		ListOwnedBoatsMenu()
	elseif PointType == 'boat_store_point' then
		StoreOwnedBoatsMenu()
	elseif PointType == 'boat_pound_point' then
		ReturnOwnedBoatsMenu()
	elseif PointType == 'police_pound_point' then
		ReturnOwnedPoliceMenu()
	elseif PointType == 'ambulance_pound_point' then
		ReturnOwnedAmbulanceMenu()
	end
end
function refreshBlips()

	for k,v in pairs(Config.CarPounds) do
		local blip = AddBlipForCoord(table.unpack({ v.PoundPoint.x, v.PoundPoint.y }))

		SetBlipSprite (blip, Config.BlipPound.Sprite)
		SetBlipColour (blip, Config.BlipPound.Color)
		--SetBlipDisplay(blip, Config.Blips.Pounds.Display)
		SetBlipScale  (blip, 0.6)
		SetBlipAsShortRange(blip, true)

		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString(_U('blip_pound'))
		EndTextCommandSetBlipName(blip)
		table.insert(BlipList, blip)
	end
end

-- Handles Private Blips
function DeletePrivateBlips()
	if PrivateBlips[1] ~= nil then
		for i=1, #PrivateBlips, 1 do
			RemoveBlip(PrivateBlips[i])
			PrivateBlips[i] = nil
		end
	end
end


exports('setGarage', function(data)
    this_Garage = data
end)

exports('OpenGarageMenu', OpenGarageMenu)

function RefreshPrivateBlips()
end

-- Handles Job Blips
function DeleteJobBlips()
	if JobBlips[1] ~= nil then
		for i=1, #JobBlips, 1 do
			RemoveBlip(JobBlips[i])
			JobBlips[i] = nil
		end
	end
end

function RefreshJobBlips()
	if Config.Ambulance.Garages and Config.Ambulance.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'ambulance' then
			for k,v in pairs(Config.AmbulanceGarages) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JGarages.Sprite)
				SetBlipColour (blip, Config.Blips.JGarages.Color)
				SetBlipDisplay(blip, Config.Blips.JGarages.Display)
				SetBlipScale  (blip, Config.Blips.JGarages.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_ambulance_garage'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Ambulance.Pounds and Config.Ambulance.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'ambulance' then
			for k,v in pairs(Config.AmbulancePounds) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JPounds.Sprite)
				SetBlipColour (blip, Config.Blips.JPounds.Color)
				SetBlipDisplay(blip, Config.Blips.JPounds.Display)
				SetBlipScale  (blip, Config.Blips.JPounds.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_ambulance_pound'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Police.Garages and Config.Police.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'police' then
			for k,v in pairs(Config.PoliceGarages) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JGarages.Sprite)
				SetBlipColour (blip, Config.Blips.JGarages.Color)
				SetBlipDisplay(blip, Config.Blips.JGarages.Display)
				SetBlipScale  (blip, Config.Blips.JGarages.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_police_garage'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Police.Pounds and Config.Police.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'police' then
			for k,v in pairs(Config.PolicePounds) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JPounds.Sprite)
				SetBlipColour (blip, Config.Blips.JPounds.Color)
				SetBlipDisplay(blip, Config.Blips.JPounds.Display)
				SetBlipScale  (blip, Config.Blips.JPounds.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_police_pound'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Mechanic.Garages and Config.Mechanic.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'mechanic' then
			for k,v in pairs(Config.MechanicGarages) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JGarages.Sprite)
				SetBlipColour (blip, Config.Blips.JGarages.Color)
				SetBlipDisplay(blip, Config.Blips.JGarages.Display)
				SetBlipScale  (blip, Config.Blips.JGarages.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_mechanic_garage'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Taxi.Garages and Config.Taxi.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'taxi' then
			for k,v in pairs(Config.TaxiGarages) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JGarages.Sprite)
				SetBlipColour (blip, Config.Blips.JGarages.Color)
				SetBlipDisplay(blip, Config.Blips.JGarages.Display)
				SetBlipScale  (blip, Config.Blips.JGarages.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString('Taxi')
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end

	if Config.Mechanic.Pounds and Config.Mechanic.Blips then
		if ESX.PlayerData.job and ESX.PlayerData.job.name == 'mechanic' then
			for k,v in pairs(Config.MechanicPounds) do
				local blip = AddBlipForCoord(v.Marker)

				SetBlipSprite (blip, Config.Blips.JPounds.Sprite)
				SetBlipColour (blip, Config.Blips.JPounds.Color)
				SetBlipDisplay(blip, Config.Blips.JPounds.Display)
				SetBlipScale  (blip, Config.Blips.JPounds.Scale)
				SetBlipAsShortRange(blip, true)

				BeginTextCommandSetBlipName("STRING")
				AddTextComponentString(_U('blip_mechanic_pound'))
				EndTextCommandSetBlipName(blip)
				table.insert(JobBlips, blip)
			end
		end
	end
end


RegisterNetEvent('event:tes', function()
	-- print('HALOW')
end)
