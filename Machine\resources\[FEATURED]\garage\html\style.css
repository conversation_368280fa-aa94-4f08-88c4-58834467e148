* {
  --time: 30s;
  box-sizing: border-box;
}

body {
  overflow-y: hidden;
  font-family: verdana;
  background-color: transparent; /* change to TRANSPARENT */
  text-transform:uppercase;
}

.main {
  width: 80%;
  height: 80%;
  /*background-color: rgba(90,90,90,0.5);*/
  position: relative;
  top: 50%;
  left: 60%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: block;

}

#ContainerOne{
  color: white;
  top: 25%;
  left: 55%; /*495px;*/
  margin-top: -50px;
  width: 290px;
  height: 798px;
  /*position: fixed;!*absolute;*!*/
  display: inline-block;
  position: relative;
  vertical-align:top;
}

#ContainerTwo{
  color: white;
  top: 25%;
  left: 21%; /*495px;*/
  margin-top: -50px;
  width: 200px;
  height: 798px;
  /*position: fixed;!*absolute;*!*/
  display: inline-block;
  position: relative;
  vertical-align:top;
}

#ContainerThree{
  color: white;
  top: 15%;
  left: 5%; /*495px;*/
  margin-top: -50px;
  width: 420px;
  height: 798px;
  /*position: fixed;!*absolute;*!*/
  display: inline-block;
  position: relative;
  vertical-align:top;
}


.title{
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #FFB700;
  margin-bottom: 4px;
  padding-left: 15px;
  text-align: center;
  border-radius: 1vh
}
.title2{
  display: flex;
  align-items: center;
  font-size: 16px;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #FFB700;
  margin-bottom: 4px;
  padding-left: 15px;
  text-align: center;
  border-radius: 1vh;
}

.title3{
  display: flex;
  align-items: center;
  font-size: 16px;
  margin-left: 6px;
  color: white;
  height: 30px;
  font-weight: 600;
  width: 420px;
  background: #FFB700;
  margin-bottom: 4px;
  padding-left: 15px;
  text-align: center;
  border-radius: 1vh;
}

.info{
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 6px;
  color: white;
  height: 120px;
  width: 200px;
  background: #25262998;/*#0C5CBC;*/
  margin-top: 2px;
  padding-left: 15px;
  border-radius: 1vh;
}

.Wrap{
  height: 36%;
  overflow-y: scroll;
}

.info2{
  margin-top: -6px;
  height: 50%;
  overflow-y: scroll;
  background: #FFB700;/*#0C5CBC;*/
}


.listallowed{
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #25262998;/*#0C5CBC;*/
  margin-top: 2px;
  padding-left: 15px;
  border-radius: 1vh;
}
.listallowed.activecat{
  /*background: #2c3e50;*/
  background: #1a1b1e;
  color: white;
}

.listallowed.selected{
  /*background: #25C3C5;*/
  background: #1a1b1e;
  color: white;
  /*box-shadow: 1px 1px #2c3e50;*/
}

.listdisallowed{
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #25262998;/*#0C5CBC;*/
  margin-top: 2px;
  padding-left: 15px;
}
.listdisallowed.activecat{
  /*background: #2c3e50;*/
  background: #FFB700;
  color: white;
}

.listdisallowed.selected{
  /*background: #25C3C5;*/
  background: #FFB700;/*#0C5CBC;*/
  color: white;
  /*box-shadow: 1px 1px #2c3e50;*/
}

.searchvehicles{
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #25262998;/*#0C5CBC;*/
  margin-top: 2px;
  padding-left: 15px;
  outline: none;
  border: none;
  text-transform: uppercase;
  border-radius: 1vh;
}
::placeholder{
  color: white;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: white;
}

::-ms-input-placeholder { /* Microsoft Edge */
  color: white;
}


.list{
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #25262998;/*#0C5CBC;*/
  margin-top: 2px;
  padding-left: 15px;
}
.list.activecat{
  background: #1a1b1e;
  color: white;
}

.list.selected{
  background: #1a1b1e;
  color: white;
  /*box-shadow: 1px 1px #2c3e50;*/
}

.takeout{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
  border-radius: 1vh;
}

.takeout.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout.selected{
  background: #1a1b1e;
  color: white;
}

.takeout2{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout2.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout2.selected{
  background: #1a1b1e;
  color: white;
}

.takeout3{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout3.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout3.selected{
  background: #1a1b1e;
  color: white;
}

.takeout4{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout4.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout4.selected{
  background: #1a1b1e;
  color: white;
}

.takeout5{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout5.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout5.selected{
  background: #1a1b1e;
  color: white;
}

.takeout6{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout6.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout6.selected{
  background: #1a1b1e;
  color: white;
}

.takeout7{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout7.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout7.selected{
  background: #1a1b1e;
  color: white;
}

.takeout8{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
}

.takeout8.activecat{
  background: #1a1b1e;
  color: white;
}

.takeout8.selected{
  background: #1a1b1e;
  color: white;
}

.trunk_check{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 200px;
  background: #165a72;
  margin-top: 5px;
  padding-left: 15px;
}

.trunk_check.activecat{
  background: #313247;
  color: white;
}

.trunk_check.selected{
  background: #313247;
  color: white;
}

.exitBtn{
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 6px;
  color: white;
  height: 30px;
  width: 275px;
  background: #25262998;
  margin-top: 5px;
  padding-left: 15px;
  border-radius: 1vh;
}

.exitBtn.activecat{
  background: #25262998;
  color: white;
}

.exitBtn.selected{
  background: #1a1b1e;
  color: white;
}

::-webkit-scrollbar-button{
  display: block;
  height: 13px;
  border-radius: 0px;
  background-color: #AAA;
}
::-webkit-scrollbar-button:hover{
  background-color: #AAA;
}
::-webkit-scrollbar-thumb{
  background-color: #5badd9;
}
::-webkit-scrollbar-thumb:hover{
  background-color: #CCC;
}
::-webkit-scrollbar-track{
  background-color: #5c5c5c;
}
::-webkit-scrollbar-track:hover{
  background-color: #CCC;
}
::-webkit-scrollbar{
  width: 4px;
}

.slot1  {
  float: left;
  width: 97px;
  height: 120px;
  background-color: #25262998;
  margin-left: 6px;
  margin-top: 6px;
  position: relative;
  border: 0.5px solid rgba(161, 161, 159, 0.1);
}

.item {
  width: 97px;
  height: 120px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center center;

}

.item-name {
  position: absolute;
  bottom: 0;
  text-align: center;
  padding-top: 5px;
  padding-bottom: 5px;
  width: 96px;
  min-height: 15px;
  z-index: 500;
  font-size: 10px;
  background-color: rgba(19, 6, 6, 0.3);
}
.item-count {
  margin-top: 1px;
  position: absolute;
  top: 0;
  text-align: right;
  width: 94px;
  height: 20px;
  z-index: 500;
  font-size: 11px;
}
.item-key {
  font-family: Arial, Helvetica, sans-serif;
  font-weight: 800;
  position: absolute;
  text-align: center;
  font-size: 12px;
  line-height: 0px;
  color: #000000;
  width: fit-content;
  top: 0;
  z-index: 999;
  background-color: rgba(255, 255, 255, 1.0);
  padding: 10px;
  border-right: 1px solid #666666;
  border-bottom: 1px solid #666666;
}
