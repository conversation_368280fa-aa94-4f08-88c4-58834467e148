ESX = exports["r_core"]:getSharedObject()

RegisterCommand('givecar', function(source, args)
	givevehicle(source, args, 'car', 'donasi')
end)

RegisterCommand('giveplane', function(source, args)
	givevehicle(source, args, 'airplane', 'donasi')
end)

RegisterCommand('giveboat', function(source, args)
	givevehicle(source, args, 'boat', 'donasi')
end)

RegisterCommand('giveheli', function(source, args)
	givevehicle(source, args, 'helicopter', 'donasi')
end)

function sendGivecarLog(xPlayerName, carModel, plate, targetPlayerName)
	local logs2 = "https://discord.com/api/webhooks/1369425208942919710/rPE2qW8A56ywBF53GYBsrwzuADj0zLdf5VhHrYK2FyI_bZ9RjRF40vJCbQbduvh7_lFo"
	local embed = {
		{
			color = 1942002,
			title = "Menggunakan fitur givecar",
			description = string.format("**Nama admin: **%s\n**Kendaraan: **%s\n**Plat Kendaraan: **%s\n**Ke player: **%s", xPlayerName, carModel, plate, targetPlayerName),
			footer = {
				text = "supplyrp",
				icon_url = communtiylogo,
			},
		}
	}
	PerformHttpRequest(logs2, function() end, 'POST', json.encode({ username = "SUPPLY LOGS", embeds = embed }), {
		['Content-Type'] = 'application/json'
	})
end

function givevehicle(_source, _args, vehicleType, permission)
    if not havePermission(_source, permission) then
        return TriggerClientEvent('ox_lib:notify', _source, {
            type = 'error',
            description = 'Kamu tidak memiliki akses!'
        })
    end

    if not _args[1] or not _args[2] then
        return TriggerClientEvent('esx:showNotification', _source, '/givevehicle playerID carModel [plate]')
    end

    local targetId = tonumber(_args[1])
    local carModel = _args[2]

    if not GetPlayerName(targetId) then
        return TriggerClientEvent('ox_lib:notify', _source, {
            type = 'error',
            description = 'Player tidak ditemukan.'
        })
    end

    local playerName = GetPlayerName(targetId)

    -- Ambil plate jika diinput, jika tidak biarkan nil (otomatis random)
    local plate = nil
    if _args[3] then
        plate = table.concat(_args, " ", 3)
        plate = string.upper(plate)
    end

    -- Ambil data kendaraan dari client (client bisa generate plate jika nil)
    local vehProps = lib.callback.await('rise-givecar:client:vehicleData', _source, carModel, plate, playerName)
    Wait(500)

    if not vehProps then
        return TriggerClientEvent('ox_lib:notify', _source, {
            type = 'error',
            description = 'Terjadi kesalahan saat givecar!'
        })
    end

    masukanDB(vehProps, targetId, vehicleType)

    local xPlayer = ESX.GetPlayerFromId(_source)
    sendGivecarLog(xPlayer.getName(), carModel, vehProps.plate or plate or "UNKNOWN", playerName)
end


RegisterCommand('hapuscar', function(source, args)
	if havePermission(source, 'donasi') then
		if args[1] == nil then
			TriggerClientEvent('ox_lib:notify', source, {
				type = 'error',
				description = '/delcarplate <plate>'
			})
		else
			local plate = args[1]
			if #args > 1 then
				for i = 2, #args do
					plate = plate.." "..args[i]
				end
			end
			plate = string.upper(plate)

			local result = MySQL.Sync.execute('DELETE FROM owned_vehicles WHERE plate = @plate', {
				['@plate'] = plate
			})
			if result == 1 then
				TriggerClientEvent('ox_lib:notify', source, {
					type = 'success',
					description = 'Berhasil menghapus kendaraan dengan plat '.. plate
				})
			elseif result == 0 then
				TriggerClientEvent('ox_lib:notify', source, {
					type = 'error',
					description = 'Kendaraan dengan plat '.. plate ..' tidak ditemukan'
				})
			end
		end
	else
		TriggerClientEvent('ox_lib:notify', source, {
			type = 'error',
			description = 'Anda tidak memiliki izin untuk melakukan perintah ini!'
		})
	end
end)

function masukanDB(vehicleProps, playerID, vehicleType)
    local xPlayer = ESX.GetPlayerFromId(playerID)
    if not xPlayer then
        print(("[ERROR] Player dengan ID %s tidak ditemukan."):format(playerID))
        return
    end

    if not vehicleProps or not vehicleProps.plate then
        print(("[ERROR] Data kendaraan tidak valid untuk player ID %s."):format(playerID))
        return
    end

	local garageName = 'Garasi_Import'
	if vehicleType == 'boat' then
		garageName = 'Garasi_Boat'
	elseif vehicleType == 'aircraft' then
		garageName = 'Garasi_Air'
	end

	MySQL.Async.execute('INSERT INTO owned_vehicles (owner, plate, vehicle, stored, garage, type) VALUES (@owner, @plate, @vehicle, @stored, @garage, @type)', {
		['@owner']   = xPlayer.identifier,
		['@plate']   = vehicleProps.plate,
		['@vehicle'] = json.encode(vehicleProps),
		['@stored']  = 1,
		['@garage']  = garageName,
		['@type']    = vehicleType
	}, function ()
		if Config.ReceiveMsg then
			TriggerClientEvent('ox_lib:notify', xPlayer.source, {
				type = 'success',
				description = 'Anda menerima kendaraan dengan nomor plat ['.. string.upper(vehicleProps.plate)..']'
			})
		end
	end)
end

function havePermission(_source, permission)
	local xPlayer = ESX.GetPlayerFromId(_source)
	local playerGroup = xPlayer.getGroup()
	local isAllowed = false

	for _, group in pairs(Config.AuthorizedRanks) do
		if group == playerGroup then
			isAllowed = true
			break
		end
	end

	if IsPlayerAceAllowed(_source, permission) then isAllowed = true end

	return isAllowed
end

lib.callback.register('admin:canUseCommand', function(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return false end

    local group = xPlayer.getGroup()
    return group == 'admin' or group == 'donasi'
end)

lib.callback.register('admin:getVehiclesByIdentifier', function(source, identifier)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return {} end

    local group = xPlayer.getGroup()
    if group ~= 'admin' and group ~= 'donasi' then return {} end

    local result = MySQL.query.await('SELECT plate, vehicle FROM owned_vehicles WHERE owner LIKE ?', {'%' .. identifier})
    return result or {}
end)

RegisterServerEvent("admin:updateVehiclePlate")
AddEventHandler("admin:updateVehiclePlate", function(oldPlate, newPlate)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local group = xPlayer.getGroup()
    if group ~= 'admin' and group ~= 'donasi' then return end

    local result = MySQL.single.await('SELECT vehicle FROM owned_vehicles WHERE plate = ?', {oldPlate})
    if not result then return end

    local vehicleData = json.decode(result.vehicle)
    if not vehicleData then return end

    vehicleData.plate = newPlate

    MySQL.update.await('UPDATE owned_vehicles SET plate = ?, vehicle = ? WHERE plate = ?', {
        newPlate,
        json.encode(vehicleData),
        oldPlate
    })
end)

RegisterServerEvent("admin:deleteVehicle")
AddEventHandler("admin:deleteVehicle", function(plate)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local group = xPlayer.getGroup()
    if group ~= 'admin' and group ~= 'donasi' then return end

    MySQL.update.await('DELETE FROM owned_vehicles WHERE plate = ?', {plate})
end)

RegisterNetEvent('admin:moveVehicleToGarage', function(plate, garageName)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    local adminIdentifier = GetPlayerIdentifier(src, 0)

    local group = xPlayer.getGroup()
    if group ~= 'admin' and group ~= 'donasi' then return end

    if not plate or not garageName then
        print("[ADMIN] ❌ Parameter plate atau garageName tidak valid.")
        return
    end

    if not Config.CarGarages[garageName] then
        print(('[ADMIN] ❌ Garasi "%s" tidak terdaftar di Config.'):format(garageName))
        return
    end

    local result = MySQL.query.await('SELECT * FROM owned_vehicles WHERE plate = ?', {plate})

    if not result or #result == 0 then
        print(('[ADMIN] ❌ Kendaraan dengan plate [%s] tidak ditemukan di database.'):format(plate))
        return
    end

    local update = MySQL.update.await('UPDATE owned_vehicles SET garage = ?, stored = 1 WHERE plate = ?', {
        garageName,
        plate
    })

    if update and update > 0 then
        print(('[ADMIN] ✅ %s memindahkan kendaraan [%s] ke garasi [%s] dan menyimpannya (stored = 1)'):format(adminIdentifier or "Unknown Admin", plate, garageName))
    else
        print(('[ADMIN] ❌ Gagal update kendaraan [%s] di database.'):format(plate))
    end
end)


ESX.RegisterServerCallback('rise-givecar:isPlateTaken', function(src, cb, plate)
    local result = MySQL.scalar.await('SELECT 1 FROM owned_vehicles WHERE plate = ?', {plate})
    cb(result ~= nil)
end)

RegisterServerEvent("admincar:claimOwnership")
AddEventHandler("admincar:claimOwnership", function(plate)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if xPlayer.getGroup() == "admin" then
        MySQL.Async.execute('UPDATE owned_vehicles SET owner = @owner WHERE plate = @plate', {
            ['@owner'] = xPlayer.identifier,
            ['@plate'] = plate
        }, function(rowsChanged)
            if rowsChanged == 0 then
                TriggerClientEvent('rise-notify:Client:SendAlert', src, {
                    type = 'error',
                    text = ('❌ Plate %s tidak ditemukan di database!'):format(plate)
                })
            else
                TriggerClientEvent('rise-notify:Client:SendAlert', src, {
                    type = 'success',
                    text = ('✅ Kamu sekarang menjadi pemilik kendaraan %s'):format(plate)
                })
            end
        end)
    else
        TriggerClientEvent('rise-notify:Client:SendAlert', src, {type = 'error',text = "Kamu mencoba menggunakan command donasi tanpa izin.",})
    end
end)
