Citizen.CreateThread(function()
    -- AddTextEntry('modelName', 'label')
    -- AddTextEntry('vehicleMakeName', 'label')
    -- Example: 
    -- AddTextEntry('gtr', "Skyline GTR '17") -- For the car name itself
    -- AddTextEntry('Nissan', 'Nissan') -- For the make name (in vehicles.meta -> <vehicleMakeName>)
    -- MOBIL S
    AddTextEntry('RS1ELBY', 'FERD SHELBA')
    AddTextEntry('RS1EVOX', 'MITSHUBAHSA UVO X')
    AddTextEntry('RS1R35', 'NUSSAN GTR35')
    AddTextEntry('RS2CVCR', 'HINDI CAVUC')
    AddTextEntry('RS2LC500', 'LC 500')
    AddTextEntry('RS2X8', 'RX8')
    AddTextEntry('RS3FL400', 'FL400')
    AddTextEntry('RS3M4ADR', 'M4ADRO')
    AddTextEntry('RS3WRXSTI', 'WRXSTI')
    AddTextEntry('RS4CHLG', 'DGD CHLG')
    AddTextEntry('RS4GLC63', 'GLC63')
    AddTextEntry('RS4M8', 'MW M8')
    AddTextEntry('RS4NC1', 'HINDI NSX')

    -- MOBIL S+
    AddTextEntry('RS1HUAYRAR', 'PAGANA HAUYRAR')
    AddTextEntry('RS1POR', 'PURSCHA 911')
    AddTextEntry('RS1SF90XX', 'FERURRO SF90')
    AddTextEntry('RS2SKO', 'KNSG JKO')
    AddTextEntry('RS2W1', 'MCLORE W1')
	AddTextEntry('RS3AVENT', 'EVENTADOR')
	AddTextEntry('RS3R34', 'R34')
    AddTextEntry('RS4A80', 'TOYOTO SUPRA')
	AddTextEntry('RS4BGT', 'BGT SS')
    AddTextEntry('RSO3R34', 'R34 FF')	
    AddTextEntry('RSOG900', 'G900')
	AddTextEntry('RSOR8', 'R8')
	AddTextEntry('RSOS63', 'S63')	
	AddTextEntry('RSOVRS6', 'AUDA RS6')	
		
    -- MOBIL A
    AddTextEntry('RA1BL', 'EW ROCCO')
    AddTextEntry('RA1C7', 'CHEVROLIT C7')
    AddTextEntry('RA1CITY', 'HINDI CITI')
    AddTextEntry('RA1CYBER', 'TISLI CYBER')
    AddTextEntry('RA1M5', 'MW M5')
    AddTextEntry('RA2G63', 'G63')
    AddTextEntry('RA2GRY', 'TOYOTO GRY')
	AddTextEntry('RA2STAGEA', 'STAGEA')
    AddTextEntry('RA3F80', 'MW F80')
	AddTextEntry('RA3RRMNSR', 'RR MANSORY')
    AddTextEntry('RA3S63', 'S63')
    AddTextEntry('RA4JIMNY', 'SUZUKA JMY')
    AddTextEntry('RA4PLSD', 'HND PALISADE')
    AddTextEntry('RA4RS3', 'AUDA RS3')
	AddTextEntry('RA1500SHEL', 'FERD 500 SHELBA')

    -- MOBIL B
    AddTextEntry('RB1EV', 'WULING')
    AddTextEntry('RB1HARD', 'TOYOTO PHARD')
    AddTextEntry('RB1LANDCSR', 'TOYOTO CRUISER')
    AddTextEntry('RB1NOVA', 'TOYOTO INNOVO')
    AddTextEntry('RB1RAM1500', 'DOGDE RAM1500')
    AddTextEntry('RB1TITAN', 'NISSIN TITIN')
    AddTextEntry('RB1X7', 'MW X7')
	AddTextEntry('RB2LX570', 'LX570')
    AddTextEntry('RB2UNER', 'FORUNER')
    AddTextEntry('RB3FJCSR', 'FJCRUISER')
    AddTextEntry('RB3JEEP', 'JEEP GLADIATOR')
    AddTextEntry('RB3LXSLM', 'LX LM')	
    AddTextEntry('RB4JERO15FZ', 'MITSHUBAHSA JERO15FZ')
    AddTextEntry('RB4NOVA12FZ', 'TOYOTO INV12FZ')	

    -- MOBIL C
    AddTextEntry('RC1HORNET', 'WULING')
    AddTextEntry('RC1S8', 'TOYOTO PHARD')

    -- MOBIL D
    AddTextEntry('RD1KOT', 'SUZUKA CARRY')
    AddTextEntry('RD1MAX', 'HINDI MAX')
    AddTextEntry('RD1MAXBOX', 'HINDI MAXBOX')
    AddTextEntry('RD1NJA', 'NJA')
    AddTextEntry('RD1SWIFT', 'SUZUKA WIFT')
    AddTextEntry('foodvan', 'Van Rasa+')

    -- MOTOR S
    AddTextEntry('RMS1R1', 'YIMIHI R1')
    AddTextEntry('RMS1R6', 'YIMIHI YZF F6')
    AddTextEntry('RMS1S1000R', 'MW S1000RR')
	AddTextEntry('RMS2DVL', 'DIAVEL')
	AddTextEntry('RMS2RSV', 'RSV4')
	AddTextEntry('RMS3H2R', 'H2R')
    AddTextEntry('RMS4ZX10', 'ZX10')

    -- MOTOR A
    AddTextEntry('RMA1DVD', 'HARLE DOVODSON')
    AddTextEntry('RMA1GS1', 'MW GS1200')
    AddTextEntry('RMA1NC7', 'YIMIHI NC7')
    AddTextEntry('RMA1STAR', 'HARLE BLUESTIR')
    AddTextEntry('RMA1SXV', 'APRIL SXV')
    AddTextEntry('RMA1Y450F', 'YIMIHI 450F')
    AddTextEntry('RMA2CVOC18', 'CVOC')
	AddTextEntry('RMA2GER', 'GER')
	AddTextEntry('RMA2HLY883', 'HLY883')
	AddTextEntry('RMA2LOYAL', 'RIID KING')
	AddTextEntry('RMA2Z1000A', 'Z1000')
	AddTextEntry('RMA3LR', 'LOW RIDER')
	AddTextEntry('RMA3WC', 'WILDCHILD')
    AddTextEntry('RMA4BY', 'BY')
    AddTextEntry('RMA4DARYLJ', 'DARYLJ')

    -- MOTOR B
    AddTextEntry('RMB1CB', 'HINDI CB')
    AddTextEntry('RMB1CHARIOT', 'HARLE CHAR')
	AddTextEntry('RMB2VESPA', 'VESPA')
	AddTextEntry('RMB3NX', 'NMX')
    AddTextEntry('RMB4SCPY', 'HINDI SCPY')
    AddTextEntry('RMB4SUPRA', 'HINDI SUPRA')

    -- HELICOPTER
    AddTextEntry('2vd_conada', 'CONAD')
    AddTextEntry('2vd_supervolito', 'SUPER VOLIT')
    AddTextEntry('AS350', 'AS350')
    AddTextEntry('aw139', 'MEDICAL AW139')
    AddTextEntry('emsvol', 'MEDICAL VOLMAV')
    AddTextEntry('polmav', 'POLICE MAVERICK')
    AddTextEntry('polmav2pol', 'POLICE MAVERICK 2')

    -- WHITELIST MOBIL
    AddTextEntry('RAEMS1RAM', 'DOGDE RAM')
    AddTextEntry('RAPOL1ERO', 'MITSHUBASHA PAJARO')
    AddTextEntry('RAPOL1EVO9', 'MITSHUBASHA EVO')
    AddTextEntry('RAPOL1FD', 'HINDI CIVOC FD')
    AddTextEntry('RAPOL1LUX', 'TOYOTO HILU')
    AddTextEntry('RAPOL1TON', 'MITSHUBAHSA TRITAN')
    AddTextEntry('RAPOL1TOR', 'FERD RAPTAR')
    AddTextEntry('RAPOL163', 'BENZ G63')
    AddTextEntry('RASTATE1G700', 'BENZ G700')
    AddTextEntry('RASTATE1S500', 'BENZ S500')
    AddTextEntry('RASTATES1500l', 'BENZ S1500L')
    AddTextEntry('RBEMS1ANCE', 'MEDICAL AMBULANCE')
    AddTextEntry('RDPOL1LANTAS', 'MITSHUBASHA FASI')
    AddTextEntry('RDPOL1SABHARA', 'MITSHUBASHA FASO')
    AddTextEntry('RSEMS1RS7', 'AUDA RS7')
    AddTextEntry('RSPOL1MAN', 'PURSCHA CALMAN')
    AddTextEntry('RSPOL1ZDA6', 'MZD 6')
    AddTextEntry('RCPOL1CUDDA', 'BARRACUDDA')
    AddTextEntry('RCPOL1RIOT', 'RIOT')
    AddTextEntry('RCPOL1SURGENT', 'INSURGENT')
    AddTextEntry('RSEMSGT63', 'GT63 P.EMS')
    AddTextEntry('RS2SKO', 'JESKO')

    -- WHITELIST MOTOR
    AddTextEntry('RAPOL1LX250', 'KAWASAKA LX250')
    AddTextEntry('RAPOL1XR', 'MW SX')
    AddTextEntry('RBPOL1LX150', 'KAWASAKA LX150')
    AddTextEntry('RMSPOL1H2', 'KAWASAKA H2')
    AddTextEntry('RSPOL1GS', 'MW GS1200')
    AddTextEntry('RSPOL1WING', 'HINDI GOLDWING')
end)